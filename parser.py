import requests
import os
import json
import urllib3
import argparse
import time
import re
import subprocess
import shlex
import shutil
from urllib.parse import urlparse, urljoin
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
from datetime import datetime
from processing_tracker import ProcessingTracker, ProcessingStage, StatusCode
from bs4 import BeautifulSoup

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Get your Scrapeless API key from: https://scrapeless.com
SCRAPELESS_API_KEY = os.environ.get('SCRAPELESS_API')

# Get your Scrape Ninja API key from: https://apiroad.net/marketplace/apis/scrapeninja
SCRAPENINJA_API_KEY = os.environ.get('SCRAPENINJA_API_KEY')

# Get your Jina AI API key for free: https://jina.ai/?sui=apikey
JINA_API_KEY = "jina_6e74a6c7f9384fe287aa46871bc4a7571PyajnjS84i0KizZoDSqegL1MQ_O"

# Get your Google Gemini API key: https://ai.google.dev/
GEMINI_API_KEY = os.environ.get('GEMINI_API_KEY', 'AIzaSyBi5AuSUtli5nvVEcJehEEoL8BhmlXT8ZU')

"""
Usage examples:
1. Crawl a single website:
   python parser.py https://example.com

2. Crawl and parse a single website:
   python parser.py https://example.com --parse

3. Process a list of websites (one URL per line):
   python parser.py --list websites.txt

4. Process a list of websites and parse each one:
   python parser.py --list websites.txt --parse

5. Parse an existing crawled file:
   python parser.py --parse-file example.com.txt

6. Use Jina Direct mode (curl-based) to crawl and save content:
   python parser.py https://example.com --jina-direct

7. Process websites from a specific CSV file with location data:
   python parser.py --csv locations.csv

8. Process all CSV files in the 'csv' folder:
   python parser.py --csv

9. Process all JSON files in the 'first_batch' folder:
   python parser.py --json

10. Process all CSV files in the 'csv' folder with Jina Direct mode:
    python parser.py --csv --jina-direct

 11. Process a specific JSON file:
     python parser.py --json example.json

 12. Skip websites that have been previously crawled or failed:
    python parser.py --list websites.txt --skip-previous

11. Generate a status report without running any processing:
    python parser.py --status-report

12. Force processing of websites even if they were previously crawled or failed:
    python parser.py --list websites.txt --skip-previous --force
"""

# Jina AI Reader API endpoint
reader_api_url = 'https://r.jina.ai/'

class WebsiteCrawler:
    def __init__(self, start_url, output_file=None, max_pages=50, delay=1,
                 exclude_patterns=None, failure_tracker=None, raw_dir=None):
        self.start_url = start_url
        # Flag to track if preprocessing has been applied
        self.preprocessing_applied = False
        # Extract domain and remove 'www.' if present
        domain = urlparse(start_url).netloc
        self.domain = domain.replace('www.', '') if domain.startswith('www.') else domain

        # Generate timestamp for consistent file naming
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Determine the output file path
        if raw_dir:
            # Create the directory if it doesn't exist
            os.makedirs(raw_dir, exist_ok=True)

            # Use domain name and timestamp in the output file, but place it in the specified directory
            self.output_file = os.path.join(raw_dir, f"{self.domain}_{self.timestamp}_crawled.txt")
        else:
            # Use domain name and timestamp in the output file if not specified
            if output_file is None:
                self.output_file = f"{self.domain}_{self.timestamp}_crawled.txt"
            else:
                self.output_file = output_file

        self.max_pages = max_pages
        self.delay = delay
        self.visited_urls = set()
        self.queue = [start_url]
        self.session = self._create_session()

        # Initialize processing tracker
        self.failure_tracker = failure_tracker or ProcessingTracker()

        # Initialize lock file descriptor
        self.lock_fd = None

        # Default patterns to exclude non-content links (widgets, popups, etc.)
        self.exclude_patterns = exclude_patterns or [
            # Widget patterns
            'widget', 'ottowidget', 'chatwidget', 'livechat',
            # Popup/modal patterns
            'popup', 'modal', 'overlay', 'lightbox', 'dialog',
            # Interactive elements
            'tooltip', 'dropdown', 'menu', 'nav-toggle',
            # Authentication related
            'login', 'signin', 'signup', 'register', 'auth',
            # Utility links
            'print', 'email', 'share', 'bookmark', 'favorite',
            # Media viewers
            'gallery', 'slideshow', 'carousel', 'player',
            # Forms and actions
            'form', 'feedback', 'subscribe', 'newsletter',
            # Tracking and analytics
            'track', 'analytics', 'utm_', 'campaign',
            # Miscellaneous
            'ajax', 'callback', 'action', 'function'
        ]

        # Create or clear the output file
        with open(self.output_file, 'w', encoding='utf-8') as f:
            f.write(f"# Website Crawl Results\n")
            f.write(f"# Starting URL: {start_url}\n")
            f.write(f"# Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

    def _create_session(self):
        """Create a session with retry strategy"""
        session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("https://", adapter)
        session.mount("http://", adapter)
        return session

    def _is_internal_link(self, url):
        """Check if a URL is internal to the domain and has a valid web scheme"""
        parsed_url = urlparse(url)
        # Check if scheme is http or https and domain matches
        valid_scheme = parsed_url.scheme in ['http', 'https', '']
        return valid_scheme and (parsed_url.netloc == self.domain or not parsed_url.netloc)

    def _is_widget_link(self, url):
        """
        Check if a URL should be excluded from crawling.

        This checks for:
        1. Widget, popup, or other non-content links using exclude patterns
        2. Media files (images, videos, documents, etc.)
        3. WordPress content directories and other CMS media paths
        4. Any other non-content URLs that shouldn't be crawled
        """
        parsed_url = urlparse(url)

        # Convert all parts of the URL to lowercase for case-insensitive matching
        path = parsed_url.path.lower()
        query = parsed_url.query.lower()
        fragment = parsed_url.fragment.lower()
        full_url = url.lower()

        # 1. Check for common image file extensions
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp', '.ico', '.tiff']
        if any(path.endswith(ext) for ext in image_extensions):
            return True

        # 2. Check for other media file extensions
        media_extensions = [
            # Video files
            '.mp4', '.webm', '.ogg', '.mov', '.avi', '.wmv', '.flv', '.mkv',
            # Audio files
            '.mp3', '.wav', '.ogg', '.m4a', '.aac',
            # Document files
            '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
            # Archive files
            '.zip', '.rar', '.tar', '.gz', '.7z',
            # Font files
            '.ttf', '.otf', '.woff', '.woff2', '.eot'
        ]
        if any(path.endswith(ext) for ext in media_extensions):
            return True

        # 3. Check for WordPress and other CMS media directories
        cms_media_patterns = [
            '/wp-content/',
            '/uploads/',
            '/media/',
            '/assets/',
            '/images/',
            '/img/',
            '/static/',
            '/files/',
            '/attachments/',
            '/downloads/',
            '/articles/',
            '/blog/'
        ]
        if any(pattern in path for pattern in cms_media_patterns):
            # Allow specific content types within these directories
            allowed_content_types = ['.html', '.php', '.aspx', '.jsp']
            if not any(path.endswith(ext) for ext in allowed_content_types):
                return True

        # 4. Check all parts of the URL for exclude patterns
        url_parts = [path, query, fragment]
        for part in url_parts:
            if any(pattern.lower() in part for pattern in self.exclude_patterns):
                return True

        return False

    def _normalize_url(self, url, base_url):
        """Normalize URL to absolute form"""
        # Check if URL has a valid scheme
        parsed_url = urlparse(url)
        if parsed_url.scheme not in ['http', 'https', '']:
            return None  # Skip non-http/https URLs

        # Handle relative URLs
        if not parsed_url.netloc:
            url = urljoin(base_url, url)

        # Remove fragments
        url = url.split('#')[0]

        # Remove trailing slash for consistency
        if url.endswith('/'):
            url = url[:-1]

        # If the base URL is HTTPS, ensure all internal links use HTTPS too
        # This helps maintain protocol consistency within the site
        base_parsed = urlparse(base_url)
        url_parsed = urlparse(url)
        if (base_parsed.scheme == 'https' and
            url_parsed.scheme == 'http' and
            url_parsed.netloc == base_parsed.netloc):
            url = 'https://' + url[7:]  # Replace http:// with https://

        return url

    def _parse_page(self, url):
        """Parse a page using Jina AI Reader API with retry mechanism"""
        headers = {
            'Accept': 'application/json',
            'Authorization': f'Bearer {JINA_API_KEY}',
            'Content-Type': 'application/json',
            'X-Engine': 'browser',
            'X-Retain-Images': 'none',
            'X-With-Links-Summary': 'true'
        }

        data = {'url': url}

        # Define retry parameters
        max_retries = 3
        retry_delay = 2  # Initial delay in seconds
        retry_status_codes = [429, 500, 502, 503, 504]  # Status codes to retry on

        for attempt in range(max_retries):
            try:
                # Try with SSL verification first
                try:
                    response = self.session.post(reader_api_url, headers=headers, json=data, timeout=30)
                    response.raise_for_status()
                except requests.exceptions.SSLError:
                    print(f"SSL error encountered for {url}, retrying without SSL verification...")
                    response = self.session.post(reader_api_url, headers=headers, json=data, verify=False, timeout=30)
                    response.raise_for_status()

                # If we get here, the request was successful
                result = response.json()

                if 'data' in result:
                    title = result['data'].get('title', 'No title')
                    content = result['data'].get('content', 'No content')
                    links = result['data'].get('links', {})

                    # Extract all internal links
                    internal_links = []
                    for link_text, link_url in links.items():
                        normalized_url = self._normalize_url(link_url, url)
                        # Check if it's a valid internal link and not a widget link
                        if (normalized_url and
                            self._is_internal_link(normalized_url) and
                            not self._is_widget_link(normalized_url) and
                            normalized_url not in self.visited_urls):
                            internal_links.append(normalized_url)

                    return {
                        'title': title,
                        'content': content,
                        'internal_links': internal_links
                    }
                else:
                    error_msg = f"No data in response for {url}"
                    print(f"Error: {error_msg}")
                    # Don't retry for missing data - this is likely a permanent issue
                    self.failure_tracker.add_crawl_failure(url, error_msg)
                    return None

            except requests.exceptions.HTTPError as e:
                status_code = e.response.status_code if hasattr(e, 'response') else 0

                # Check if we should retry based on status code
                if status_code in retry_status_codes and attempt < max_retries - 1:
                    wait_time = retry_delay * (2 ** attempt)  # Exponential backoff
                    print(f"Received {status_code} error for {url}, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})...")
                    time.sleep(wait_time)
                    continue
                else:
                    error_msg = f"HTTP error: {e}"
                    print(f"Error parsing {url}: {error_msg}")
                    self.failure_tracker.add_crawl_failure(url, error_msg)
                    return None

            except (requests.exceptions.ConnectionError,
                    requests.exceptions.Timeout,
                    requests.exceptions.RequestException) as e:
                # Network-related errors that might be temporary
                if attempt < max_retries - 1:
                    wait_time = retry_delay * (2 ** attempt)  # Exponential backoff
                    print(f"Connection error for {url}, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})...")
                    time.sleep(wait_time)
                    continue
                else:
                    error_msg = f"Connection error after {max_retries} attempts: {e}"
                    print(f"Error parsing {url}: {error_msg}")
                    self.failure_tracker.add_crawl_failure(url, error_msg)
                    return None

            except Exception as e:
                # For any other exceptions, log and return None
                error_msg = f"Unexpected error: {e}"
                print(f"Unexpected error parsing {url}: {error_msg}")
                self.failure_tracker.add_crawl_failure(url, error_msg)
                return None

        # If we've exhausted all retries
        error_msg = f"Failed after {max_retries} attempts"
        print(f"Failed to parse {url} after {max_retries} attempts")
        self.failure_tracker.add_crawl_failure(url, error_msg)
        return None

    def _save_content(self, url, title, content):
        """Save content to the output file"""
        with open(self.output_file, 'a', encoding='utf-8') as f:
            f.write(f"\n\n{'=' * 80}\n")
            f.write(f"URL: {url}\n")
            f.write(f"TITLE: {title}\n")
            f.write(f"{'=' * 80}\n\n")
            f.write(content)

    def _try_https_if_http_fails(self, url):
        """
        Try HTTPS version of a URL if HTTP version fails.
        Returns the page data if successful, None otherwise.
        """
        # Only attempt protocol switch for HTTP URLs
        if not url.startswith('http://'):
            return None

        # Create HTTPS version of the URL
        https_url = 'https://' + url[7:]
        print(f"HTTP failed, trying HTTPS version: {https_url}")

        # Try to parse with HTTPS
        return self._parse_page(https_url)

    def crawl(self):
        """Crawl the website starting from the start_url"""
        pages_crawled = 0

        print(f"Starting crawl from {self.start_url}")
        print(f"Results will be saved to {self.output_file}")

        # Try to acquire a lock for this website
        if isinstance(self.failure_tracker, ProcessingTracker):
            lock_fd = self.failure_tracker.try_acquire_lock(self.start_url)
            if not lock_fd:
                print(f"Could not acquire lock for {self.start_url}. It may be being processed by another instance.")
                return

            # Store the lock file descriptor
            self.lock_fd = lock_fd

            # Update status to indicate crawling has started
            self.failure_tracker.update_status(
                self.start_url,
                ProcessingStage.CRAWLING,
                StatusCode.IN_PROGRESS,
                details=f"Starting crawl with max {self.max_pages} pages"
            )

        try:
            while self.queue and pages_crawled < self.max_pages:
                # Get the next URL from the queue
                current_url = self.queue.pop(0)

                # Skip if already visited or is a widget link
                if current_url in self.visited_urls or self._is_widget_link(current_url):
                    continue

                print(f"Crawling {pages_crawled + 1}/{self.max_pages}: {current_url}")

                # Parse the page
                page_data = self._parse_page(current_url)

                # If HTTP failed, try HTTPS
                if not page_data and current_url.startswith('http://'):
                    page_data = self._try_https_if_http_fails(current_url)
                    if page_data:
                        # Update the URL in the visited set to the HTTPS version
                        https_url = 'https://' + current_url[7:]
                        print(f"Successfully switched to HTTPS: {https_url}")
                        current_url = https_url

                if page_data:
                    # Mark as visited
                    self.visited_urls.add(current_url)
                    pages_crawled += 1

                    # Save content
                    self._save_content(current_url, page_data['title'], page_data['content'])

                    # Add internal links to the queue
                    for link in page_data['internal_links']:
                        if link not in self.visited_urls and link not in self.queue:
                            self.queue.append(link)

                    # Respect rate limits
                    if self.delay > 0:
                        time.sleep(self.delay)

            print(f"Crawl completed. Visited {pages_crawled} pages.")
            print(f"Results saved to {self.output_file}")

            # Update status to indicate crawling is complete
            if isinstance(self.failure_tracker, ProcessingTracker):
                self.failure_tracker.update_status(
                    self.start_url,
                    ProcessingStage.CRAWLING,
                    StatusCode.SUCCESS,
                    details=f"Completed crawling {pages_crawled} pages"
                )

        except Exception as e:
            # Update status to indicate crawling failed
            if isinstance(self.failure_tracker, ProcessingTracker):
                self.failure_tracker.update_status(
                    self.start_url,
                    ProcessingStage.CRAWLING,
                    StatusCode.FAILURE,
                    details=f"Crawling failed: {str(e)}"
                )
            raise  # Re-raise the exception to be handled by the caller






def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(
        description='Crawl a website and save content to a file',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  1. Use Scrapeless API to crawl a site:
     python parser.py https://example.com --scrapeless

  2. Use Scrapeless API with parsing:
     python parser.py https://example.com --scrapeless --parse

  3. Process a list of sites with Scrapeless:
     python parser.py --list sites.txt --scrapeless

  4. Use Scrape Ninja API to crawl a site:
     python parser.py https://example.com --ninja

  5. Use Scrape Ninja API with parsing:
     python parser.py https://example.com --ninja --parse

  6. Process a list of sites with Scrape Ninja:
     python parser.py --list sites.txt --ninja
""")
    parser.add_argument('url', nargs='?', help='Starting URL to crawl')
    parser.add_argument('--output', '-o', help='Output file path (defaults to domain name)')
    parser.add_argument('--max-pages', '-m', type=int, default=50, help='Maximum number of pages to crawl')
    parser.add_argument('--delay', '-d', type=float, default=1, help='Delay between requests in seconds')
    parser.add_argument('--exclude', '-e', nargs='+', help='Additional patterns to exclude from crawling')
    parser.add_argument('--parse', '-p', action='store_true', help='Process crawled content with Gemini API')
    parser.add_argument('--parse-file', '-f', help='Parse an existing file with Gemini API (skips crawling)')
    parser.add_argument('--gemini-key', '-g', help='Google Gemini API key (overrides environment variable)')
    parser.add_argument('--max-tokens', '-t', type=int, default=8000, help='Maximum number of tokens for Gemini API response')
    parser.add_argument('--output-dir', '-od', default='parsed_results', help='Directory to save parsed results')
    parser.add_argument('--list', '-l', help='Path to a text file containing a list of websites to crawl (one URL per line)')
    parser.add_argument('--failures-dir', '-fd', default='failed_sites', help='Directory to save information about failed sites')
    parser.add_argument('--csv', '-c', nargs='?', const=True, help='Process CSV files with location data. If no filename provided, processes all files in the "csv" folder')
    parser.add_argument('--json', '-js', nargs='?', const=True, help='Process JSON files to find and crawl websites. Can be a JSON file path or directory containing JSON files. If no path provided, processes all files in the "first_batch" folder')
    parser.add_argument('--no-preprocess', action='store_true', help='Disable content preprocessing (keep all links)')
    parser.add_argument('--jina-direct', '-jd', action='store_true', help='Use curl to extract links and fetch content directly from r.jina.ai')
    parser.add_argument('--scrapeless', '-sl', action='store_true', help='Use Scrapeless API to render JavaScript and fetch content')
    parser.add_argument('--ninja', '-n', action='store_true', help='Use Scrape Ninja API to render JavaScript and fetch content with markdown extraction')
    parser.add_argument('--link-depth', '-ld', type=int, default=1, help='Maximum depth for link discovery (1=only homepage links, 2=also crawl links from subpages, etc.)')
    parser.add_argument('--skip-previous', action='store_true', help='Skip websites that have been previously crawled or failed')
    parser.add_argument('--force', action='store_true', help='Force crawling even if website was previously crawled or failed')
    parser.add_argument('--status-report', action='store_true', help='Generate a status report without running any processing')

    args = parser.parse_args()

    # Set the Gemini API key in environment if provided via command line
    if args.gemini_key:
        os.environ['GEMINI_API_KEY'] = args.gemini_key
        print(f"Using provided Gemini API key")

    # Check for incompatible flags
    crawler_flags = [args.scrapeless, args.jina_direct, args.ninja]
    if sum(crawler_flags) > 1:
        parser.error("Cannot use multiple crawler flags together (--scrapeless, --jina-direct, --ninja)")

    # Initialize the processing tracker

    # Initialize the processing tracker
    status_dir = os.path.join(args.failures_dir, "status")
    locks_dir = os.path.join(args.failures_dir, "locks")

    # Create the processing tracker with enhanced status tracking
    failure_tracker = ProcessingTracker(
        output_dir=args.failures_dir,
        parsed_dir=args.output_dir,
        status_dir=status_dir,
        locks_dir=locks_dir
    )
    print(f"Processing status will be tracked and saved to {status_dir}")
    print(f"Failures will be automatically tracked and saved to {args.failures_dir}")

    # If skip-previous is enabled, print information about loaded domains
    if args.skip_previous and not args.force:
        print("Skip-previous mode enabled: Will skip websites that have been previously crawled or failed")
        if failure_tracker.previously_failed_domains:
            print(f"Loaded {len(failure_tracker.previously_failed_domains)} previously failed domains")
        if failure_tracker.previously_crawled_domains:
            print(f"Loaded {len(failure_tracker.previously_crawled_domains)} previously crawled domains")

    # If status-report is enabled, generate a report and exit
    if args.status_report:
        if isinstance(failure_tracker, ProcessingTracker):
            print("\n=== GENERATING PROCESSING STATUS REPORT ===")
            status_report_file = failure_tracker.save_status_report()

            # Generate a summary of the processing status
            report = failure_tracker.generate_status_report()
            summary = report["summary"]

            print("\n=== PROCESSING STATUS SUMMARY ===")
            print(f"Total sites processed: {summary['total_sites']}")
            print(f"Completed successfully: {summary['completed_sites']}")
            print(f"Partially completed: {summary['partial_sites']}")
            print(f"Failed: {summary['failed_sites']}")
            print(f"In progress: {summary['in_progress_sites']}")
            print(f"Not started: {summary['not_started_sites']}")
            print(f"Success rate: {summary['success_rate']:.2f}%")

            # Print some details about failed sites
            if summary['failed_sites'] > 0:
                print("\n=== FAILED SITES ===")
                for url in report["sites_by_status"]["failed"][:10]:  # Show first 10 failed sites
                    site_status = report["detailed_status"][url]
                    failed_stage = next((stage for stage, status in site_status.get("stages", {}).items()
                                        if status.get("status") == StatusCode.FAILURE), "unknown")
                    failure_details = site_status.get("stages", {}).get(failed_stage, {}).get("details", "No details available")
                    print(f"- {url}: Failed at {failed_stage} stage - {failure_details}")

                if len(report["sites_by_status"]["failed"]) > 10:
                    print(f"... and {len(report['sites_by_status']['failed']) - 10} more failed sites")

            # Print some details about partial sites
            if summary['partial_sites'] > 0:
                print("\n=== PARTIALLY COMPLETED SITES ===")
                for url in report["sites_by_status"]["partial"][:10]:  # Show first 10 partial sites
                    site_status = report["detailed_status"][url]
                    partial_stage = next((stage for stage, status in site_status.get("stages", {}).items()
                                         if status.get("status") == StatusCode.PARTIAL), "unknown")
                    partial_details = site_status.get("stages", {}).get(partial_stage, {}).get("details", "No details available")
                    print(f"- {url}: Partial completion at {partial_stage} stage - {partial_details}")

                if len(report["sites_by_status"]["partial"]) > 10:
                    print(f"... and {len(report['sites_by_status']['partial']) - 10} more partially completed sites")

            print(f"\nDetailed status report saved to: {status_report_file}")
        else:
            print("Status report generation requires the ProcessingTracker. This feature is not available with the basic FailureTracker.")

        return

    # Create output directories if parsing is requested
    if args.parse or args.parse_file or args.csv or args.json:
        # Ensure the output directory exists
        os.makedirs(args.output_dir, exist_ok=True)

        # Create a subfolder for all raw and intermediate files
        raw_dir = os.path.join(args.output_dir, "raw")
        os.makedirs(raw_dir, exist_ok=True)

        print(f"Output will be saved to {args.output_dir}")
        print(f"Raw and intermediate files will be saved to {raw_dir}")

    # Check if we're processing JSON files to find websites
    if args.json:
        # Get the raw directory for all intermediate files
        raw_dir = os.path.join(args.output_dir, "raw")
        os.makedirs(raw_dir, exist_ok=True)

        # Get custom exclude patterns if provided
        exclude_patterns = None
        if args.exclude:
            exclude_patterns = args.exclude

        # List to store all JSON files to process
        json_files_to_process = []

        # Handle JSON file/folder processing
        if args.json is True:  # No path provided, use default first_batch folder
            target_path = 'first_batch'
            if not os.path.exists(target_path):
                parser.error("first_batch folder not found. Create a 'first_batch' folder and place your JSON files there.")
        else:
            target_path = args.json
            if not os.path.exists(target_path):
                parser.error(f"Path not found: {target_path}")

        # Check if target is a directory or file
        if os.path.isdir(target_path):
            # Get all JSON files from the directory
            json_files = [os.path.join(target_path, f) for f in os.listdir(target_path) if f.endswith('.json')]
            if not json_files:
                parser.error(f"No JSON files found in the directory: {target_path}")
            json_files_to_process = json_files
            print(f"Found {len(json_files)} JSON files to process in {target_path}")
        else:
            # Single JSON file
            if not target_path.endswith('.json'):
                parser.error(f"File must be a JSON file: {target_path}")
            json_files_to_process = [target_path]
            print(f"Processing JSON file: {target_path}")

        # Process each JSON file
        for json_file in json_files_to_process:
            print(f"\n=== Processing JSON file: {json_file} ===")

            try:
                # Read the JSON file to extract websites
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # Function to recursively find URLs only from 'website' fields in JSON data
                def extract_urls(obj):
                    urls = []
                    if isinstance(obj, dict):
                        for key, value in obj.items():
                            if key == 'website' and isinstance(value, str):
                                # Simple URL validation
                                if value.startswith(('http://', 'https://')):
                                    urls.append(value)
                                elif any(value.startswith(domain + '.') or value == domain for domain in ['www', 'http', 'https']):
                                    urls.append('http://' + value)
                            else:
                                urls.extend(extract_urls(value))
                    elif isinstance(obj, list):
                        for item in obj:
                            urls.extend(extract_urls(item))
                    return urls

                # Extract all website URLs from the JSON data
                websites = extract_urls(data)

                if not websites:
                    print(f"Warning: No valid website URLs found in the JSON file: {json_file}")
                    continue

                print(f"Found {len(websites)} websites in {json_file}")

                # Process each website
                for i, website in enumerate(websites):
                    print(f"\n[{i+1}/{len(websites)}] Processing website: {website}")

                    try:
                        # Check if we should skip this website based on previous crawls or failures
                        if args.skip_previous and not args.force:
                            should_skip, reason = failure_tracker.check_website_status(website)
                            if should_skip:
                                print(f"Skipping {website}: {reason}")
                                continue

                        # Check if we're using Jina Direct mode
                        if args.jina_direct:
                            print("Using Jina Direct mode (curl-based crawler)")

                            # Create and run the Jina Direct crawler for this website
                            crawler = JinaDirectCrawler(
                                start_url=website,
                                output_file=None,  # Always use domain name
                                max_pages=args.max_pages,
                                delay=args.delay,
                                exclude_patterns=exclude_patterns,
                                failure_tracker=failure_tracker,
                                raw_dir=raw_dir,
                                no_preprocess=args.no_preprocess,
                                link_depth=args.link_depth
                            )

                            # Crawl the website using curl and Jina AI
                            crawler.crawl()

                            # Process with Gemini if API key is available
                            api_key = os.environ.get('GEMINI_API_KEY', GEMINI_API_KEY)
                            if api_key:
                                crawler.process_with_gemini(output_dir=args.output_dir, no_preprocess=args.no_preprocess)
                            else:
                                print("Warning: Gemini API key not found. Skipping content processing.")

                        else:
                            # Standard crawling mode
                            crawler = WebsiteCrawler(
                                start_url=website,
                                output_file=None,  # Always use domain name
                                max_pages=args.max_pages,
                                delay=args.delay,
                                exclude_patterns=exclude_patterns,
                                failure_tracker=failure_tracker,
                                raw_dir=raw_dir
                            )

                            # Crawl the website
                            crawler.crawl()

                            # Process with Gemini if API key is available
                            api_key = os.environ.get('GEMINI_API_KEY', GEMINI_API_KEY)
                            if api_key:
                                crawler.process_with_gemini(output_dir=args.output_dir, no_preprocess=args.no_preprocess)
                            else:
                                print("Warning: Gemini API key not found. Skipping content processing.")

                    except Exception as e:
                        error_msg = f"Error processing {website}: {str(e)}"
                        print(f"Error: {error_msg}")
                        failure_tracker.add_crawl_failure(website, error_msg)

                print(f"\nCompleted processing {len(websites)} websites from {json_file}")

            except Exception as e:
                error_msg = f"Error processing JSON file {json_file}: {e}"
                print(f"Error: {error_msg}")
                failure_tracker.add_crawl_failure(json_file, f"JSON processing error: {e}")

    # Check if we're processing CSV files with location data
    if args.csv:
        # Import csv module
        import csv

        # Get the raw directory for all intermediate files
        raw_dir = os.path.join(args.output_dir, "raw")
        os.makedirs(raw_dir, exist_ok=True)

        # Get custom exclude patterns if provided
        exclude_patterns = None
        if args.exclude:
            exclude_patterns = args.exclude

        # List to store all CSV files to process
        csv_files_to_process = []

        # Check if a specific CSV file was provided or if we should process all files in the csv folder
        if args.csv is True:  # No filename provided, process all files in csv folder
            # Check if the csv folder exists
            if not os.path.exists('csv'):
                parser.error("CSV folder not found. Create a 'csv' folder and place your CSV files there.")

            # Get all CSV files from the csv folder
            csv_files = [os.path.join('csv', f) for f in os.listdir('csv') if f.endswith('.csv')]

            if not csv_files:
                parser.error("No CSV files found in the 'csv' folder.")

            csv_files_to_process = csv_files
            print(f"Found {len(csv_files)} CSV files to process in the 'csv' folder.")
        else:
            # A specific CSV file was provided
            if not os.path.exists(args.csv):
                parser.error(f"CSV file not found: {args.csv}")

            csv_files_to_process = [args.csv]
            print(f"Processing CSV file: {args.csv}")

        # Process each CSV file
        for csv_file in csv_files_to_process:
            print(f"\n=== Processing CSV file: {csv_file} ===")

            try:
                # Read the CSV file to extract websites and location information
                with open(csv_file, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    locations = list(reader)

                if not locations:
                    print(f"Warning: No valid locations found in the CSV file: {csv_file}")
                    continue

                print(f"Found {len(locations)} locations in {csv_file}")

                # Process each location
                for i, location in enumerate(locations):
                    name = location.get('Name', f"Location {i+1}")
                    address = location.get('Formatted Address', 'Unknown address')
                    website = location.get('Website')

                    print(f"\n[{i+1}/{len(locations)}] Processing: {name}")
                    print(f"Address: {address}")

                    if not website:
                        error_msg = "No website URL provided in CSV"
                        print(f"Error: {error_msg}")
                        failure_tracker.add_crawl_failure(name, error_msg)
                        continue

                    # Normalize website URL
                    if not website.startswith('http://') and not website.startswith('https://'):
                        website = 'http://' + website
                        print(f"Added protocol to URL: {website}")

                    try:
                        # Check if we should skip this website based on previous crawls or failures
                        if args.skip_previous and not args.force:
                            should_skip, reason = failure_tracker.check_website_status(website)
                            if should_skip:
                                print(f"Skipping {website}: {reason}")
                                continue

                        # Check which crawler mode to use
                        if args.scrapeless:
                            print("Using Scrapeless API mode")
                            crawler = ScrapelessCrawler(
                                start_url=website,
                                output_file=None,  # Always use domain name for CSV mode
                                max_pages=args.max_pages,
                                delay=args.delay,
                                exclude_patterns=exclude_patterns,
                                failure_tracker=failure_tracker,
                                raw_dir=raw_dir,
                                no_preprocess=args.no_preprocess,
                                link_depth=args.link_depth
                            )
                        elif args.ninja:
                            print("Using Scrape Ninja API mode")
                            crawler = ScrapeNinjaCrawler(
                                start_url=website,
                                output_file=None,  # Always use domain name for CSV mode
                                max_pages=args.max_pages,
                                delay=args.delay,
                                exclude_patterns=exclude_patterns,
                                failure_tracker=failure_tracker,
                                raw_dir=raw_dir,
                                no_preprocess=args.no_preprocess,
                                link_depth=args.link_depth
                            )
                        elif args.jina_direct:
                            print("Using Jina Direct mode (curl-based crawler)")
                            crawler = JinaDirectCrawler(
                                start_url=website,
                                output_file=None,  # Always use domain name for CSV mode
                                max_pages=args.max_pages,
                                delay=args.delay,
                                exclude_patterns=exclude_patterns,
                                failure_tracker=failure_tracker,
                                raw_dir=raw_dir,
                                no_preprocess=args.no_preprocess,
                                link_depth=args.link_depth
                            )

                            # Crawl the website using curl and Jina AI
                            crawler.crawl()

                            # Process with Gemini
                            # Check if Gemini API key is available in environment
                            api_key = os.environ.get('GEMINI_API_KEY', GEMINI_API_KEY)
                            if not api_key:
                                print("Error: Gemini API key is required for parsing.")
                                print("Please provide it using --gemini-key option or set the GEMINI_API_KEY environment variable.")
                                continue

                            # Initialize the parser with location info
                            from gemini_parser import GeminiParser
                            parser = GeminiParser(api_key=api_key)
                            parser.set_location_info(location)

                            # Process with Gemini
                            crawler.process_with_gemini(output_dir=args.output_dir, no_preprocess=args.no_preprocess, location_info=location)
                        else:
                            # Standard crawling mode
                            # Create and run the crawler for this website
                            crawler = WebsiteCrawler(
                                start_url=website,
                                output_file=None,  # Always use domain name for CSV mode
                                max_pages=args.max_pages,
                                delay=args.delay,
                                exclude_patterns=exclude_patterns,
                                failure_tracker=failure_tracker,
                                max_depth=args.link_depth
                            )

                            # Crawl the website
                            crawler.crawl()

                            # Process with Gemini
                            # Check if Gemini API key is available in environment
                            api_key = os.environ.get('GEMINI_API_KEY', GEMINI_API_KEY)
                            if not api_key:
                                print("Error: Gemini API key is required for parsing.")
                                print("Please provide it using --gemini-key option or set the GEMINI_API_KEY environment variable.")
                                continue

                            # Initialize the parser with location info
                            from gemini_parser import GeminiParser
                            parser = GeminiParser(api_key=api_key)
                            parser.set_location_info(location)

                            # Process with Gemini
                            crawler.process_with_gemini(output_dir=args.output_dir, no_preprocess=args.no_preprocess, location_info=location)

                    except Exception as e:
                        error_msg = f"Error processing {name}: {str(e)}"
                        print(f"Error: {error_msg}")
                        failure_tracker.add_crawl_failure(website, error_msg)

                print(f"\nCompleted processing {len(locations)} locations from {csv_file}")

            except Exception as e:
                error_msg = f"Error processing CSV file {csv_file}: {e}"
                print(f"Error: {error_msg}")
                failure_tracker.add_crawl_failure(csv_file, f"CSV processing error: {e}")

        # Sort JSON files into Dog_cat folder if they only contain cats and/or dogs
        sort_json_files(output_dir=args.output_dir)

        # Always save failures if any are detected
        if failure_tracker.has_failures():
            # Save both JSON and text formats
            json_file = failure_tracker.save_failures()
            text_file = failure_tracker.save_as_text()

            print(f"\nFailed sites information saved to:")
            print(f"- JSON format: {json_file}")
            print(f"- Text format: {text_file}")
            print(f"Total failures: {len(failure_tracker.get_all_failed_urls())}")
        else:
            print("\nNo failures detected during this run.")

        # Generate and save a comprehensive status report if using ProcessingTracker
        if isinstance(failure_tracker, ProcessingTracker):
            status_report_file = failure_tracker.save_status_report()

            # Generate a summary of the processing status
            report = failure_tracker.generate_status_report()
            summary = report["summary"]

            print("\n=== PROCESSING STATUS SUMMARY ===")
            print(f"Total sites processed: {summary['total_sites']}")
            print(f"Completed successfully: {summary['completed_sites']}")
            print(f"Partially completed: {summary['partial_sites']}")
            print(f"Failed: {summary['failed_sites']}")
            print(f"In progress: {summary['in_progress_sites']}")
            print(f"Not started: {summary['not_started_sites']}")
            print(f"Success rate: {summary['success_rate']:.2f}%")
            print(f"\nDetailed status report saved to: {status_report_file}")

        return

    # Check if we're just parsing an existing file
    if args.parse_file:
        # Use the GeminiParser class instead of the standalone parser
        from gemini_parser import GeminiParser

        # Get the API key
        api_key = os.environ.get('GEMINI_API_KEY', GEMINI_API_KEY)
        if not api_key:
            print("Error: Gemini API key is required for parsing.")
            print("Please provide it using --gemini-key option or set the GEMINI_API_KEY environment variable.")
            return

        # Output directories are already created at the beginning of the function
        raw_dir = os.path.join(args.output_dir, "raw")

        # Read the content from the file
        try:
            with open(args.parse_file, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"Error reading input file: {e}")
            return

        # Initialize the parser and parse the content
        parser = GeminiParser(api_key=api_key)

        # Extract domain from file name or use file name without extension
        file_basename = os.path.basename(args.parse_file)
        # Try to extract domain from filename if it follows the pattern domain.txt
        if '.' in file_basename and not file_basename.startswith('crawled_content'):
            base_filename = file_basename.split('.')[0]  # Use part before first dot as domain
        else:
            # Fall back to the file name without extension
            base_filename = os.path.splitext(file_basename)[0]

        # Remove 'www.' if present in the base filename
        base_filename = base_filename.replace('www.', '') if base_filename.startswith('www.') else base_filename

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Raw response file path (for debugging) - in the raw subfolder
        raw_output_file = os.path.join(raw_dir, f"{base_filename}_{timestamp}_raw.txt")

        # JSON output file - in the main output directory
        json_output_file = os.path.join(args.output_dir, f"{base_filename}_{timestamp}.json")

        # Parse the content
        print(f"Parsing content from {args.parse_file}...")

        # Save the raw content to a file for debugging
        with open(raw_output_file, 'w', encoding='utf-8') as f:
            f.write(content)

        # Parse the content using the updated GeminiParser
        # Save preprocessed content in the raw directory for debugging
        preprocessed_file = os.path.join(raw_dir, f"{base_filename}_{timestamp}_preprocessed.txt")

        # Check if preprocessing is disabled
        # For direct file parsing, try to extract the URL from the content or use the filename
        start_url = None
        url_match = re.search(r'# Starting URL:\s*(.*?)$', content, re.MULTILINE)
        if url_match:
            start_url = url_match.group(1).strip()
        else:
            # Try to use the filename as a hint for the URL
            if args.parse_file and '/' in args.parse_file:
                potential_domain = os.path.basename(args.parse_file).split('_')[0]
                if '.' in potential_domain:  # Looks like a domain
                    start_url = f"https://{potential_domain}"
                    print(f"Using filename as homepage URL hint: {start_url}")

        if args.no_preprocess:
            print("Content preprocessing disabled (--no-preprocess flag used)")
            parsed_data = parser.parse_content(content, save_preprocessed=False, skip_preprocessing=True, homepage_url=start_url)
        else:
            parsed_data = parser.parse_content(content, save_preprocessed=True, preprocessed_file=preprocessed_file, skip_preprocessing=False, homepage_url=start_url)

        if parsed_data:
            # Check if this is partial data from a truncated response
            if parsed_data.get('partial_data'):
                print("Warning: Using partial data extracted from truncated Gemini API response")
                print("Some fields may be missing or incomplete")
                # Continue processing with the partial data we have

            # Clean the JSON data to remove special characters
            from gemini_api import clean_json_data
            cleaned_data = clean_json_data(parsed_data)

            # Save the cleaned parsed data to a JSON file
            with open(json_output_file, 'w', encoding='utf-8') as f:
                json.dump(cleaned_data, f, indent=2, ensure_ascii=False)

            print(f"Parsed data saved to {json_output_file}")
            print(f"Raw content saved to {raw_output_file}")

            # Sort JSON files into Dog_cat folder if they only contain cats and/or dogs
            sort_json_files(output_dir=args.output_dir)
        else:
            error_msg = "Failed to parse content"
            print(f"Error: {error_msg}")
            print(f"Raw content saved to {raw_output_file}")
            failure_tracker.add_parse_failure(args.parse_file, error_msg)

        return

    # Check if we're processing a list of websites
    if args.list:
        # Check if the list file exists
        if not os.path.exists(args.list):
            parser.error(f"Website list file not found: {args.list}")

        # Read the list of websites from the file
        try:
            with open(args.list, 'r', encoding='utf-8') as f:
                websites = [line.strip() for line in f if line.strip() and not line.strip().startswith('#')]

            if not websites:
                parser.error(f"No valid URLs found in the list file: {args.list}")

            print(f"Found {len(websites)} websites to process in {args.list}")

            # Get custom exclude patterns if provided
            exclude_patterns = None
            if args.exclude:
                exclude_patterns = args.exclude

            # Get the raw directory for all intermediate files
            raw_dir = os.path.join(args.output_dir, "raw") if args.parse else None

            # Process each website in the list
            for i, website_url in enumerate(websites):
                print(f"\n[{i+1}/{len(websites)}] Processing: {website_url}")

                # Check if we should skip this website based on previous crawls or failures
                if args.skip_previous and not args.force:
                    should_skip, reason = failure_tracker.check_website_status(website_url)
                    if should_skip:
                        print(f"Skipping {website_url}: {reason}")
                        continue

                # Check which crawler mode to use
                if args.scrapeless:
                    print("Using Scrapeless API mode")
                    crawler = ScrapelessCrawler(
                        start_url=website_url,
                        output_file=None,  # Always use domain name for list mode
                        max_pages=args.max_pages,
                        delay=args.delay,
                        exclude_patterns=exclude_patterns,
                        failure_tracker=failure_tracker,
                        raw_dir=raw_dir,
                        no_preprocess=args.no_preprocess,
                        link_depth=args.link_depth
                    )
                elif args.ninja:
                    print("Using Scrape Ninja API mode")
                    crawler = ScrapeNinjaCrawler(
                        start_url=website_url,
                        output_file=None,  # Always use domain name for list mode
                        max_pages=args.max_pages,
                        delay=args.delay,
                        exclude_patterns=exclude_patterns,
                        failure_tracker=failure_tracker,
                        raw_dir=raw_dir,
                        no_preprocess=args.no_preprocess,
                        link_depth=args.link_depth
                    )
                elif args.jina_direct:
                    print("Using Jina Direct mode (curl-based crawler)")

                    # Create and run the Jina Direct crawler for this website
                    crawler = JinaDirectCrawler(
                        start_url=website_url,
                        output_file=None,  # Always use domain name for list mode
                        max_pages=args.max_pages,
                        delay=args.delay,
                        exclude_patterns=exclude_patterns,
                        failure_tracker=failure_tracker,
                        raw_dir=raw_dir,
                        no_preprocess=args.no_preprocess,
                        link_depth=args.link_depth
                    )
                else:
                    # Standard crawling mode
                    # Create and run the crawler for this website
                    crawler = WebsiteCrawler(
                        start_url=website_url,
                        output_file=None,  # Always use domain name for list mode
                        max_pages=args.max_pages,
                        delay=args.delay,
                        exclude_patterns=exclude_patterns,
                        failure_tracker=failure_tracker,
                        raw_dir=raw_dir  # Use the raw directory for all intermediate files
                    )

                # Crawl the website
                crawler.crawl()

                # Process with Gemini if requested
                if args.parse:
                    # Check if Gemini API key is available in environment
                    if not os.environ.get('GEMINI_API_KEY') and not GEMINI_API_KEY:
                        print("Error: Gemini API key is required for parsing.")
                        print("Please provide it using --gemini-key option or set the GEMINI_API_KEY environment variable.")
                        break  # Stop processing if no API key is available
                    else:
                        crawler.process_with_gemini(output_dir=args.output_dir, no_preprocess=args.no_preprocess)

            print(f"\nCompleted processing {len(websites)} websites from {args.list}")

            # Sort JSON files into Dog_cat folder if they only contain cats and/or dogs
            sort_json_files(output_dir=args.output_dir)

        except Exception as e:
            error_msg = f"Error processing website list: {e}"
            print(f"Error: {error_msg}")
            failure_tracker.add_crawl_failure(args.list, error_msg)
            parser.error(error_msg)



    # If we're crawling a single website, we need a URL
    elif args.url:
        # Get custom exclude patterns if provided
        exclude_patterns = None
        if args.exclude:
            exclude_patterns = args.exclude

        # Ensure URL has proper protocol
        url = args.url
        if not url.startswith('http://') and not url.startswith('https://'):
            url = 'http://' + url
            print(f"Added protocol to URL: {url}")

        # Check if we should skip this website based on previous crawls or failures
        if args.skip_previous and not args.force:
            should_skip, reason = failure_tracker.check_website_status(url)
            if should_skip:
                print(f"Skipping {url}: {reason}")
                return

        # Determine which crawler mode to use
        if args.scrapeless:
            print("Using Scrapeless API mode")
            crawler = ScrapelessCrawler(
                start_url=url,
                output_file=args.output if not args.parse else None,
                max_pages=args.max_pages,
                delay=args.delay,
                exclude_patterns=exclude_patterns,
                failure_tracker=failure_tracker,
                raw_dir=raw_dir,
                no_preprocess=args.no_preprocess,
                link_depth=args.link_depth
            )
        elif args.ninja:
            print("Using Scrape Ninja API mode")
            crawler = ScrapeNinjaCrawler(
                start_url=url,
                output_file=args.output if not args.parse else None,
                max_pages=args.max_pages,
                delay=args.delay,
                exclude_patterns=exclude_patterns,
                failure_tracker=failure_tracker,
                raw_dir=raw_dir,
                no_preprocess=args.no_preprocess,
                link_depth=args.link_depth
            )
        elif args.jina_direct:
            print("Using Jina Direct mode (curl-based crawler)")

            # Get the raw directory for all intermediate files
            raw_dir = os.path.join(args.output_dir, "raw")

            # Create and run the Jina Direct crawler
            crawler = JinaDirectCrawler(
                start_url=url,
                output_file=args.output,
                max_pages=args.max_pages,
                delay=args.delay,
                exclude_patterns=exclude_patterns,
                failure_tracker=failure_tracker,
                raw_dir=raw_dir,
                no_preprocess=args.no_preprocess,
                link_depth=args.link_depth
            )
        else:
            # Standard crawling mode
            # Get the raw directory if parsing is requested
            raw_dir = os.path.join(args.output_dir, "raw") if args.parse else None

            # Create and run the crawler
            crawler = WebsiteCrawler(
                start_url=url,
                output_file=args.output if not args.parse else None,  # Use output file if specified and not parsing
                max_pages=args.max_pages,
                delay=args.delay,
                exclude_patterns=exclude_patterns,
                failure_tracker=failure_tracker,
                raw_dir=raw_dir  # Use the raw directory for all intermediate files
            )

        # Crawl the website
        crawler.crawl()

        # Process with Gemini if requested
        if args.parse:
            # Check if Gemini API key is available in environment
            if not os.environ.get('GEMINI_API_KEY') and not GEMINI_API_KEY:
                print("Error: Gemini API key is required for parsing.")
                print("Please provide it using --gemini-key option or set the GEMINI_API_KEY environment variable.")
            else:
                crawler.process_with_gemini(output_dir=args.output_dir, no_preprocess=args.no_preprocess)

                # Sort JSON files into Dog_cat folder if they only contain cats and/or dogs
                sort_json_files(output_dir=args.output_dir)
    else:
        parser.error("URL is required for crawling. Use --parse-file to parse an existing file or --list to process multiple websites.")

    # Always save failures if any are detected
    if failure_tracker.has_failures():
        # Save both JSON and text formats
        json_file = failure_tracker.save_failures()
        text_file = failure_tracker.save_as_text()

        print(f"\nFailed sites information saved to:")
        print(f"- JSON format: {json_file}")
        print(f"- Text format: {text_file}")
        print(f"Total failures: {len(failure_tracker.get_all_failed_urls())}")
    else:
        print("\nNo failures detected during this run.")

    # Generate and save a comprehensive status report if using ProcessingTracker
    if isinstance(failure_tracker, ProcessingTracker):
        status_report_file = failure_tracker.save_status_report()

        # Generate a summary of the processing status
        report = failure_tracker.generate_status_report()
        summary = report["summary"]

        print("\n=== PROCESSING STATUS SUMMARY ===")
        print(f"Total sites processed: {summary['total_sites']}")
        print(f"Completed successfully: {summary['completed_sites']}")
        print(f"Partially completed: {summary['partial_sites']}")
        print(f"Failed: {summary['failed_sites']}")
        print(f"In progress: {summary['in_progress_sites']}")
        print(f"Not started: {summary['not_started_sites']}")
        print(f"Success rate: {summary['success_rate']:.2f}%")
        print(f"\nDetailed status report saved to: {status_report_file}")


def is_cats_and_dogs_only(json_file_path):
    """
    Check if a JSON file only contains cats and/or dogs in the "animals_treated" array.

    Args:
        json_file_path: Path to the JSON file

    Returns:
        True if the file only contains cats and/or dogs, False otherwise
    """
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Check if the file has an animals_treated array
        if 'animals_treated' not in data:
            return False

        animals = [animal.lower() for animal in data['animals_treated']]

        # If the array is empty, return False
        if not animals:
            return False

        # Check if all animals are cats and/or dogs
        allowed_animals = ['cat', 'cats', 'dog', 'dogs']
        return all(animal in allowed_animals for animal in animals)

    except Exception as e:
        print(f"Error checking animals in {json_file_path}: {e}")
        return False

def sort_json_files(output_dir="parsed_results"):
    """
    Sort JSON files into a Dog_cat folder if they only contain cats and/or dogs.

    Args:
        output_dir: Directory containing the parsed JSON files
    """
    print("\n=== SORTING JSON FILES INTO DOG_CAT FOLDER ===")
    print(f"Checking for files with only cats and dogs in {output_dir}...")

    # Create the Dog_cat folder if it doesn't exist
    dog_cat_dir = os.path.join(output_dir, "Dog_cat")
    os.makedirs(dog_cat_dir, exist_ok=True)

    # Find all JSON files in the output directory
    json_files = [f for f in os.listdir(output_dir) if f.endswith('.json')]

    # Count of files moved
    moved_count = 0

    for json_file in json_files:
        json_file_path = os.path.join(output_dir, json_file)

        # Skip if it's a directory
        if os.path.isdir(json_file_path):
            continue

        # Check if the file only contains cats and/or dogs
        if is_cats_and_dogs_only(json_file_path):
            # Move the file to the Dog_cat folder (not copy)
            destination = os.path.join(dog_cat_dir, json_file)
            shutil.move(json_file_path, destination)
            moved_count += 1
            print(f"Moved {json_file} to Dog_cat folder (contains only cats and/or dogs)")

    if moved_count > 0:
        print(f"Moved {moved_count} files to {dog_cat_dir}")
    else:
        print("No files found with only cats and dogs")


class JinaDirectCrawler:
    """
    A crawler that uses curl to extract links from a website and then fetches content
    directly from r.jina.ai for each link.
    """
    def __init__(self, start_url, output_file=None, max_pages=50, delay=1,
                 exclude_patterns=None, failure_tracker=None, raw_dir=None,
                 no_preprocess=False, link_depth=None):
        """
        Initialize the Jina Direct Crawler.

        Args:
            start_url: The starting URL to crawl
            output_file: Optional output file path (defaults to domain name)
            max_pages: Maximum number of pages to crawl
            delay: Delay between requests in seconds
            exclude_patterns: Patterns to exclude from crawling
            failure_tracker: FailureTracker instance to record failures
            raw_dir: Directory to save all raw and intermediate files
            no_preprocess: Whether to disable content preprocessing
            link_depth: Maximum depth for link discovery (1=only homepage links, 2=also subpage links)
        """
        self.no_preprocess = no_preprocess
        self.start_url = start_url
        # Flag to track if preprocessing has been applied
        self.preprocessing_applied = False
        # Extract domain and remove 'www.' if present
        domain = urlparse(start_url).netloc
        self.domain = domain.replace('www.', '') if domain.startswith('www.') else domain

        # Track redirected domains
        self.redirected_domain = None
        self.all_domains = {self.domain}  # Set to track all domains (original and redirected)

        # Generate timestamp for consistent file naming
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Determine the output file path
        if raw_dir:
            # Create the directory if it doesn't exist
            os.makedirs(raw_dir, exist_ok=True)

            # Use domain name and timestamp in the output file, but place it in the specified directory
            self.output_file = os.path.join(raw_dir, f"{self.domain}_{self.timestamp}_crawled.txt")
        else:
            # Use domain name and timestamp in the output file if not specified
            if output_file is None:
                self.output_file = f"{self.domain}_{self.timestamp}_crawled.txt"
            else:
                self.output_file = output_file

        self.max_pages = max_pages
        self.delay = delay
        self.visited_urls = set()
        self.queue = [start_url]

        # Get the link depth from args or parameter or use default (1)
        if link_depth is not None:
            self.max_link_depth = link_depth
        else:
            # Try to get from command line args
            import sys
            try:
                # Check if --link-depth is in command line args
                if '--link-depth' in sys.argv or '-ld' in sys.argv:
                    idx = sys.argv.index('--link-depth') if '--link-depth' in sys.argv else sys.argv.index('-ld')
                    if idx + 1 < len(sys.argv):
                        self.max_link_depth = int(sys.argv[idx + 1])
                    else:
                        self.max_link_depth = 1
                else:
                    self.max_link_depth = 1
            except (ValueError, IndexError):
                self.max_link_depth = 1

        print(f"Link discovery depth set to {self.max_link_depth}")

        # Track the current depth of each URL
        self.url_depths = {start_url: 0}

        # Initialize processing tracker
        self.failure_tracker = failure_tracker or ProcessingTracker()

        # Initialize lock file descriptor
        self.lock_fd = None

        # Default patterns to exclude non-content links (widgets, popups, etc.)
        self.exclude_patterns = exclude_patterns or [
            # Widget patterns
            'widget', 'ottowidget', 'chatwidget', 'livechat',
            # Popup/modal patterns
            'popup', 'modal', 'overlay', 'lightbox', 'dialog',
            # Interactive elements
            'tooltip', 'dropdown', 'menu', 'nav-toggle',
            # Authentication related
            'login', 'signin', 'signup', 'register', 'auth',
            # Utility links
            'print', 'email', 'share', 'bookmark', 'favorite',
            # Media viewers
            'gallery', 'slideshow', 'carousel', 'player',
            # Forms and actions
            'form', 'feedback', 'subscribe', 'newsletter',
            # Tracking and analytics
            'track', 'analytics', 'utm_', 'campaign',
            # Miscellaneous
            'ajax', 'callback', 'action', 'function'
        ]

        # Create or clear the output file
        with open(self.output_file, 'w', encoding='utf-8') as f:
            f.write(f"# Website Crawl Results (Jina Direct Mode)\n")
            f.write(f"# Starting URL: {start_url}\n")
            f.write(f"# Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

    def _is_internal_link(self, url):
        """Check if a URL is internal to the domain and has a valid web scheme"""
        parsed_url = urlparse(url)
        # Check if scheme is http or https
        valid_scheme = parsed_url.scheme in ['http', 'https', '']

        # Remove 'www.' from netloc for consistent comparison
        netloc = parsed_url.netloc
        if netloc.startswith('www.'):
            netloc = netloc.replace('www.', '')

        # Check if the domain matches any of our known domains (original or redirected)
        # or if it's a relative URL (no netloc)
        return valid_scheme and (netloc in self.all_domains or not parsed_url.netloc)

    def _is_widget_link(self, url):
        """
        Check if a URL should be excluded from crawling.

        This checks for:
        1. Widget, popup, or other non-content links using exclude patterns
        2. Media files (images, videos, documents, etc.)
        3. WordPress content directories and other CMS media paths
        4. Any other non-content URLs that should not be crawled
        """
        parsed_url = urlparse(url)

        # Convert all parts of the URL to lowercase for case-insensitive matching
        path = parsed_url.path.lower()
        query = parsed_url.query.lower()
        fragment = parsed_url.fragment.lower()
        full_url = url.lower()

        # 1. Check for common image file extensions
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp', '.ico', '.tiff']
        if any(path.endswith(ext) for ext in image_extensions):
            return True

        # 2. Check for other media file extensions
        media_extensions = [
            # Video files
            '.mp4', '.webm', '.ogg', '.mov', '.avi', '.wmv', '.flv', '.mkv',
            # Audio files
            '.mp3', '.wav', '.ogg', '.m4a', '.aac',
            # Document files
            '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
            # Archive files
            '.zip', '.rar', '.tar', '.gz', '.7z',
            # Font files
            '.ttf', '.otf', '.woff', '.woff2', '.eot'
        ]
        if any(path.endswith(ext) for ext in media_extensions):
            return True

        # 3. Check for WordPress and other CMS media directories
        cms_media_patterns = [
            '/wp-content/',
            '/uploads/',
            '/media/',
            '/assets/',
            '/images/',
            '/img/',
            '/static/',
            '/files/',
            '/attachments/',
            '/downloads/',
            '/articles/',
            '/blog/'
        ]
        if any(pattern in path for pattern in cms_media_patterns):
            # Allow specific content types within these directories
            allowed_content_types = ['.html', '.php', '.aspx', '.jsp']
            if not any(path.endswith(ext) for ext in allowed_content_types):
                return True

        # 4. Check all parts of the URL for exclude patterns
        url_parts = [path, query, fragment]
        for part in url_parts:
            if any(pattern.lower() in part for pattern in self.exclude_patterns):
                return True

        return False

    def _normalize_url(self, url, base_url):
        """Normalize URL to absolute form"""
        # Check if URL has a valid scheme
        parsed_url = urlparse(url)
        if parsed_url.scheme not in ['http', 'https', '']:
            return None  # Skip non-http/https URLs

        # Handle relative URLs
        if not parsed_url.netloc:
            url = urljoin(base_url, url)

        # Remove fragments
        url = url.split('#')[0]

        # Remove trailing slash for consistency
        if url.endswith('/'):
            url = url[:-1]

        # If the base URL is HTTPS, ensure all internal links use HTTPS too
        # This helps maintain protocol consistency within the site
        base_parsed = urlparse(base_url)
        url_parsed = urlparse(url)
        if (base_parsed.scheme == 'https' and
            url_parsed.scheme == 'http' and
            url_parsed.netloc == base_parsed.netloc):
            url = 'https://' + url[7:]  # Replace http:// with https://

        return url

    def _extract_links_with_curl(self, url):
        """
        Use curl to fetch a page and extract all links from it.

        Args:
            url: The URL to fetch and extract links from

        Returns:
            A tuple of (title, content, internal_links) or None if failed
        """
        try:
            # Use curl to fetch the page content with headers to check for redirects
            # Add --compressed flag to handle gzip content and save as binary
            curl_command = f'curl -s -L -i --compressed -A "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" "{url}"'
            process = subprocess.run(shlex.split(curl_command), capture_output=True, text=False)

            if process.returncode != 0:
                error_msg = f"Curl command failed with return code {process.returncode}"
                print(f"Error: {error_msg}")
                self.failure_tracker.add_crawl_failure(url, error_msg)
                return None

            # Process binary response
            response_bytes = process.stdout

            # Try to decode the response as UTF-8, handling potential encoding issues
            try:
                # First try to find the header/body separator in binary form
                header_end_bytes = response_bytes.find(b"\r\n\r\n")
                if header_end_bytes == -1:
                    header_end_bytes = response_bytes.find(b"\n\n")

                if header_end_bytes != -1:
                    # Decode headers as ASCII (headers should always be ASCII)
                    headers = response_bytes[:header_end_bytes].decode('ascii', errors='ignore')

                    # Decode body with more flexible error handling
                    html_content_bytes = response_bytes[header_end_bytes:]

                    # Check for Content-Encoding in headers
                    if "Content-Encoding: gzip" in headers or "content-encoding: gzip" in headers.lower():
                        print("Detected gzip-encoded content, decompressing...")
                        import gzip
                        try:
                            # Find the actual start of the compressed data
                            # Skip the header separator and any additional headers
                            body_start = header_end_bytes + 4  # Skip \r\n\r\n

                            # Try to decompress
                            html_content = gzip.decompress(response_bytes[body_start:]).decode('utf-8', errors='ignore')
                        except Exception as gz_error:
                            print(f"Warning: Failed to decompress gzip content: {gz_error}")
                            # Fall back to ignoring encoding issues
                            html_content = response_bytes[header_end_bytes:].decode('utf-8', errors='ignore')
                    else:
                        # Regular content, just decode with error handling
                        html_content = response_bytes[header_end_bytes:].decode('utf-8', errors='ignore')
                else:
                    # No clear header/body separation, try to decode the whole thing
                    headers = ""
                    html_content = response_bytes.decode('utf-8', errors='ignore')
            except Exception as decode_error:
                print(f"Warning: Error decoding response: {decode_error}")
                # Last resort fallback
                headers = ""
                html_content = response_bytes.decode('utf-8', errors='ignore')

            # Check for domain redirects in the headers
            redirect_domains = []
            location_matches = re.findall(r'Location:\s*(https?://([^/\s]+))', headers, re.IGNORECASE)

            for location, domain in location_matches:
                # Remove 'www.' if present for consistent comparison
                redirect_domain = domain.replace('www.', '') if domain.startswith('www.') else domain

                # Check if this is a new domain
                if redirect_domain != self.domain and redirect_domain not in self.all_domains:
                    redirect_domains.append((redirect_domain, location))

            # If we found redirects to new domains, update our domain tracking
            if redirect_domains:
                for redirect_domain, location in redirect_domains:
                    print(f"Detected redirect to new domain: {redirect_domain} ({location})")
                    self.all_domains.add(redirect_domain)

                    # Update the redirected domain (use the last one if multiple)
                    self.redirected_domain = redirect_domain

                    # Store the original domain for file naming but don't change it
                    # This ensures file names always use the original domain
                    print(f"Added {redirect_domain} to tracked domains (original domain {self.domain} will be used for file naming)")

            # Extract title
            title_match = re.search(r'<title>(.*?)</title>', html_content, re.IGNORECASE | re.DOTALL)
            title = title_match.group(1).strip() if title_match else "No title"

            # Check for Cloudflare protection
            is_cloudflare = "Checking your browser before accessing" in html_content or "Just a moment..." in html_content
            if is_cloudflare:
                print("Detected Cloudflare protection, using Jina AI for content only")

            # Get content from Jina AI directly
            # Remove the protocol (http:// or https://) from the URL for Jina AI
            parsed_url = urlparse(url)
            jina_url = f"https://r.jina.ai/{parsed_url.netloc}{parsed_url.path}"
            if parsed_url.query:
                jina_url += f"?{parsed_url.query}"
            print(f"Fetching content from Jina AI: {jina_url}")
            jina_curl_command = f'curl -s -L --compressed -A "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" "{jina_url}"'
            jina_process = subprocess.run(shlex.split(jina_curl_command), capture_output=True, text=True)

            if jina_process.returncode != 0:
                print(f"Warning: Failed to fetch content from Jina AI for {url}")
                content = "Failed to fetch content from Jina AI"
                return None
            else:
                # Extract the content from Jina's response
                jina_content = jina_process.stdout

                # Extract title from the Jina content
                title_match = re.search(r'# (.*?)(?:\n|$)', jina_content)
                title = title_match.group(1).strip() if title_match else "No title"

                # Jina AI returns content in Markdown format with specific markers
                # First, check if it contains the Markdown marker
                if "Markdown Content:" in jina_content:
                    # Extract everything after "Markdown Content:"
                    markdown_match = re.search(r'Markdown Content:(.*?)(?:$|\Z)', jina_content, re.DOTALL)
                    if markdown_match:
                        content = markdown_match.group(1).strip()
                        print(f"Successfully extracted Markdown content ({len(content)} bytes)")
                    else:
                        content = "Could not extract Markdown content from Jina AI response"
                else:
                    # If all else fails, just use the entire response
                    if len(jina_content.strip()) > 0:
                        content = jina_content.strip()
                        print(f"Using entire Jina AI response ({len(content)} bytes)")
                    else:
                        content = "Could not extract content from Jina AI response"
                        return None

                internal_links = []

                # For Cloudflare-protected sites, add manual links
                if is_cloudflare and 'brookfallsveterinary.com' in url:
                    # Check if this is the homepage
                    is_homepage = url.rstrip('/') == f"https://{parsed_url.netloc}".rstrip('/') or url.rstrip('/') == f"http://{parsed_url.netloc}".rstrip('/')

                    # For homepage, add standard service pages
                    if is_homepage:
                        print("Adding key service pages manually for Cloudflare-protected site homepage")
                        base_domain = f"https://{parsed_url.netloc}"

                        # Add key service pages for the homepage
                        common_paths = [
                            '/service/diagnostics/',
                            '/service/surgery/',
                            '/service/dental-care/',
                            '/service/cat-dental/',
                            '/service/dog-dental/',
                            '/service/exotic-diagnostic-imaging/',
                            '/about-us/',
                            '/blog/'
                        ]

                        for path in common_paths:
                            full_url = base_domain + path
                            if full_url not in self.visited_urls and full_url not in internal_links:
                                internal_links.append(full_url)

                        print(f"Added {len(internal_links)} manual service pages to crawl")
                    else:
                        # For subpages, add related links based on the current URL path
                        print("Adding related links for Cloudflare-protected site subpage")
                        base_domain = f"https://{parsed_url.netloc}"

                        # Extract the current path components
                        path_parts = parsed_url.path.strip('/').split('/')

                        # Generate related links based on the current path
                        related_links = []

                        # If we're in a service page, add other related services
                        if len(path_parts) >= 1 and path_parts[0] == 'service':
                            # Add parent service category
                            related_links.append('/service/')

                            # Add related services based on the current service
                            if len(path_parts) >= 2:
                                current_service = path_parts[1]

                                # Add related services
                                if 'dental' in current_service:
                                    related_links.extend([
                                        '/service/dental-care/',
                                        '/service/cat-dental/',
                                        '/service/dog-dental/'
                                    ])
                                elif 'cat' in current_service or 'dog' in current_service:
                                    related_links.extend([
                                        f'/service/{path_parts[0]}-wellness-plans/',
                                        f'/service/{path_parts[0]}-vaccines/',
                                        f'/service/{path_parts[0]}-spay-neuter/'
                                    ])
                                elif 'diagnostic' in current_service:
                                    related_links.extend([
                                        '/service/cat-diagnostic-imaging/',
                                        '/service/dog-diagnostic-imaging/',
                                        '/service/exotic-diagnostic-imaging/'
                                    ])

                        # If we're in the about section, add other about pages
                        elif len(path_parts) >= 1 and (path_parts[0] == 'about-us' or path_parts[0] == 'about'):
                            related_links.extend([
                                '/about-us/staff/',
                                '/about-us/testimonials/',
                                '/about-us/tour-brook-falls/'
                            ])

                        # If we're in the blog, add other content sections
                        elif len(path_parts) >= 1 and path_parts[0] == 'blog':
                            related_links.extend([
                                '/education/',
                                '/helpful-links/',
                                '/forms/'
                            ])

                        # Always add these important pages
                        related_links.extend([
                            '/',  # Homepage
                            '/about-us/',
                            '/service/',
                            '/blog/'
                        ])

                        # Add the related links to internal_links
                        for path in related_links:
                            full_url = base_domain + path
                            if full_url not in self.visited_urls and full_url not in internal_links:
                                internal_links.append(full_url)

                        print(f"Added {len(internal_links)} related links for subpage")

                    # Extract links from the Markdown content as well
                    # Look for Markdown links [text](url)
                    markdown_link_pattern = re.compile(r'\[.*?\]\((.*?)\)', re.DOTALL)
                    markdown_links = markdown_link_pattern.findall(content)

                    # Process these links
                    for link in markdown_links:
                        normalized_url = self._normalize_url(link, url)
                        if (normalized_url and
                            self._is_internal_link(normalized_url) and
                            not self._is_widget_link(normalized_url) and
                            normalized_url not in self.visited_urls and
                            normalized_url not in internal_links):
                            internal_links.append(normalized_url)

                    if len(markdown_links) > 0:
                        print(f"Extracted {len(markdown_links)} links from Markdown content")
                elif not is_cloudflare:
                    # Normal site - extract links from HTML
                    # Extract all links
                    link_pattern = re.compile(r'<a\s+(?:[^>]*?\s+)?href="([^"]*)"', re.IGNORECASE)
                    links = link_pattern.findall(html_content)

                    # Filter and normalize links
                    for link in links:
                        normalized_url = self._normalize_url(link, url)
                        if (normalized_url and
                            self._is_internal_link(normalized_url) and
                            not self._is_widget_link(normalized_url) and
                            normalized_url not in self.visited_urls and
                            normalized_url not in internal_links):
                            internal_links.append(normalized_url)

                    # Also extract links from the Markdown content
                    markdown_link_pattern = re.compile(r'\[.*?\]\((.*?)\)', re.DOTALL)
                    markdown_links = markdown_link_pattern.findall(content)

                    for link in markdown_links:
                        normalized_url = self._normalize_url(link, url)
                        if (normalized_url and
                            self._is_internal_link(normalized_url) and
                            not self._is_widget_link(normalized_url) and
                            normalized_url not in self.visited_urls and
                            normalized_url not in internal_links):
                            internal_links.append(normalized_url)

                    print(f"Found {len(internal_links)} valid internal links to crawl")

                # If no internal links were found, try to add some common pages as a fallback
                if len(internal_links) == 0:
                    print("No internal links found, adding common pages as fallback")
                    # Get the base domain (with scheme)
                    parsed_url = urlparse(url)
                    base_domain = f"{parsed_url.scheme}://{parsed_url.netloc}"

                    # Add common pages that most websites have
                    common_pages = [
                        "/about", "/about-us", "/services", "/contact", "/contact-us",
                        "/team", "/our-team", "/staff", "/our-staff", "/doctors",
                        "/locations", "/hours", "/faq", "/faqs", "/appointments",
                        "/new-patients", "/new-clients", "/resources", "/pet-care"
                    ]

                    for page in common_pages:
                        full_url = base_domain + page
                        if full_url not in self.visited_urls and full_url not in internal_links:
                            internal_links.append(full_url)

                    print(f"Added {len(internal_links)} common pages as fallback links")
                return {
                    'title': title,
                    'content': content,
                    'internal_links': internal_links
                }

        except Exception as e:
            error_msg = f"Error extracting links with curl: {e}"
            print(f"Error: {error_msg}")
            self.failure_tracker.add_crawl_failure(url, error_msg)
            return None

    def _save_content(self, url, title, content):
        """Save content to the output file"""
        with open(self.output_file, 'a', encoding='utf-8') as f:
            f.write(f"\n\n{'=' * 80}\n")
            f.write(f"URL: {url}\n")
            f.write(f"TITLE: {title}\n")
            f.write(f"{'=' * 80}\n\n")
            f.write(content)

    def _try_https_if_http_fails(self, url):
        """
        Try HTTPS version of a URL if HTTP version fails.
        Returns the page data if successful, None otherwise.
        """
        # Only attempt protocol switch for HTTP URLs
        if not url.startswith('http://'):
            return None

        # Create HTTPS version of the URL
        https_url = 'https://' + url[7:]
        print(f"HTTP failed, trying HTTPS version: {https_url}")

        # Try to extract links with HTTPS
        return self._extract_links_with_curl(https_url)



    def process_with_preprocessing(self, output_dir="parsed_results", raw_dir=None):
        """
        Process the crawled content with preprocessing to reduce token count
        Uses the same preprocessing logic as GeminiParser for consistency

        Args:
            output_dir: Directory to save processed results
            raw_dir: Directory to save raw and intermediate files (if None, will use output_dir/raw)
        """
        print(f"Processing crawled content with preprocessing...")

        # Check if the output file exists
        if not os.path.exists(self.output_file):
            print(f"Error: Output file {self.output_file} not found.")
            return

        # Read the crawled content
        with open(self.output_file, 'r', encoding='utf-8') as f:
            content = f.read()

        try:
            # Ensure we have a valid output directory
            if not output_dir:
                output_dir = "parsed_results"
                print(f"No output directory specified, using default: {output_dir}")

            # Create main output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)
            print(f"Saving results to {output_dir}")

            # Create a subfolder for raw and intermediate files
            if not raw_dir:
                raw_dir = os.path.join(output_dir, "raw")
            os.makedirs(raw_dir, exist_ok=True)

            # Always use the original domain name (not redirected) for file naming consistency
            # This ensures all files related to the same website use the same base name
            original_domain = self.domain
            if self.redirected_domain:
                print(f"Using original domain {original_domain} for file naming (instead of redirected domain {self.redirected_domain})")
            base_filename = original_domain

            # Raw content file (for debugging) - in the raw subfolder
            raw_content_file = os.path.join(raw_dir, f"{base_filename}_{self.timestamp}_raw.txt")

            # Save the raw content to a file for debugging
            with open(raw_content_file, 'w', encoding='utf-8') as f:
                f.write(content)

            # Import GeminiParser to use its preprocessing method
            from gemini_parser import GeminiParser

            # Create a temporary instance of GeminiParser just for preprocessing
            # We don't need a valid API key since we're only using the preprocessing method
            parser = GeminiParser(api_key="dummy_key_for_preprocessing_only")

            # Check if preprocessing is disabled
            if self.no_preprocess:
                print("Content preprocessing disabled (--no-preprocess flag used)")
                preprocessed_content = content
            else:
                # Use GeminiParser's preprocessing method for consistency
                preprocessed_content = parser._preprocess_content(content)

            # Save preprocessed content in the raw directory for debugging
            preprocessed_file = os.path.join(raw_dir, f"{base_filename}_{self.timestamp}_preprocessed.txt")
            with open(preprocessed_file, 'w', encoding='utf-8') as f:
                f.write(preprocessed_content)

            print(f"Raw content saved to {raw_content_file}")
            print(f"Preprocessed content saved to {preprocessed_file}")

            # Replace the original file with the preprocessed content if preprocessing was applied
            if not self.no_preprocess:
                with open(self.output_file, 'w', encoding='utf-8') as f:
                    f.write(preprocessed_content)
                print(f"Original file updated with preprocessed content")
                # Set the flag to indicate preprocessing has been applied
                self.preprocessing_applied = True

            return preprocessed_file

        except Exception as e:
            error_msg = f"Error processing content with preprocessing: {e}"
            print(error_msg)
            self.failure_tracker.add_parse_failure(self.start_url, error_msg)
            return None

    def process_with_gemini(self, output_dir="parsed_results", no_preprocess=False, location_info=None):
        """Process the crawled content with Gemini API

        Args:
            output_dir: Directory to save parsed results
            no_preprocess: Whether to disable content preprocessing


class ScrapelessCrawler(JinaDirectCrawler):
    """A crawler that uses the Scrapeless API to render JavaScript and fetch content."""

    def __init__(self, start_url, output_file=None, max_pages=50, delay=1, exclude_patterns=None,
                 failure_tracker=None, raw_dir=None, no_preprocess=False, link_depth=1):
        super().__init__(start_url, output_file, max_pages, delay, exclude_patterns,
                        failure_tracker, raw_dir, no_preprocess, link_depth)

        self.api_key = os.environ.get('SCRAPELESS_API')
        if not self.api_key:
            raise ValueError("Scrapeless API key not found. Set SCRAPELESS_API environment variable.")

        self.endpoint = "https://api.scrapeless.com/api/v1/unlocker/request"
        self.max_retries = 3
        self.retry_delay = 5

    def _extract_links_with_curl(self, url):
        """Fetch page content using Scrapeless API and extract links."""
        try:
            html_content = self._make_api_request(url)
            if not html_content:
                return None, []

            # Parse the HTML content
            soup = BeautifulSoup(html_content, 'html.parser')

            # Extract links
            links = []
            for link in soup.find_all('a', href=True):
                href = link['href']
                if href:
                    absolute_url = urljoin(url, href)
                    if self._is_internal_link(absolute_url) and not self._is_widget_link(absolute_url):
                        links.append(absolute_url)

            # Clean the content for saving
            for tag in soup(['script', 'style', 'meta', 'link', 'iframe']):
                tag.decompose()

            return str(soup), links

        except Exception as e:
            print(f"Error parsing {url}: {str(e)}")
            if isinstance(self.failure_tracker, ProcessingTracker):
                self.failure_tracker.update_status(
                    url,
                    ProcessingStage.CRAWLING,
                    StatusCode.FAILURE,
                    details=f"Failed to parse page: {str(e)}"
                )
            return None, []

    def _make_api_request(self, url):
        """Make a request to the Scrapeless API to fetch page content."""
        try:
            headers = {
                'Content-Type': 'application/json',
                'x-api-token': self.api_key
            }

            payload = {
                "actor": "unlocker.webunlocker",
                "proxy": {"country": "US"},
                "input": {
                    "url": url,
                    "method": "GET",
                    "redirect": True,
                    "js_render": True,
                    "timeout": 30000,
                    "js_instructions": [
                        {"wait": 3000},
                        {"scroll": 0},
                        {"wait": 500},
                        {"scroll": 300},
                        {"wait": 500},
                        {"scroll": 600},
                        {"wait": 500},
                        {"scroll": 0},
                        {"wait": 1000}
                    ],
                    "block": {"resources": ["image", "stylesheet", "font"]},
                    "headers": {
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                        "Accept-Language": "en-US,en;q=0.9"
                    }
                }
            }

            for retry_count in range(self.max_retries):
                try:
                    response = self.session.post(self.endpoint, headers=headers, json=payload)
                    response.raise_for_status()
                    response_data = response.json()

                    if response_data.get('code') == 200:
                        data = response_data.get('data', '')
                        if isinstance(data, dict):
                            html_content = data.get('html', '')
                            if html_content and len(html_content) >= 100:
                                return html_content
                        elif isinstance(data, str) and len(data) >= 100:
                            if '301 Moved Permanently' not in data and '302 Found' not in data:
                                return data

                    if retry_count < self.max_retries - 1:
                        time.sleep(self.retry_delay * (2 ** retry_count))
                        continue

                    raise Exception(f"Failed to get valid response from Scrapeless API after {self.max_retries} retries")

                except Exception as e:
                    if retry_count < self.max_retries - 1:
                        time.sleep(self.retry_delay * (2 ** retry_count))
                        continue
                    raise Exception(f"Scrapeless API request failed: {str(e)}")

            return None

        finally:
            # Release the lock if we're using ProcessingTracker
            if isinstance(self.failure_tracker, ProcessingTracker) and hasattr(self, 'lock_fd'):
                self.failure_tracker.release_lock(self.lock_fd)


class ScrapeNinjaCrawler(JinaDirectCrawler):
    """A crawler that uses the Scrape Ninja API to render JavaScript and fetch content with markdown extraction."""

    def __init__(self, start_url, output_file=None, max_pages=50, delay=1, exclude_patterns=None,
                 failure_tracker=None, raw_dir=None, no_preprocess=False, link_depth=1):
        super().__init__(start_url, output_file, max_pages, delay, exclude_patterns,
                        failure_tracker, raw_dir, no_preprocess, link_depth)

        self.api_key = os.environ.get('SCRAPENINJA_API_KEY')
        if not self.api_key:
            raise ValueError("Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable.")

        self.endpoint = "https://scrapeninja.apiroad.net/scrape"
        self.max_retries = 3
        self.retry_delay = 5

    def _extract_links_with_curl(self, url):
        """Fetch page content using Scrape Ninja API and extract links."""
        try:
            markdown_content = self._make_api_request(url)
            if not markdown_content:
                return None, []

            # For markdown content, we need to extract links differently
            # Since we get markdown, we'll parse it to find links
            links = []

            # Extract markdown links [text](url) and plain URLs
            import re

            # Find markdown-style links
            markdown_links = re.findall(r'\[([^\]]*)\]\(([^)]+)\)', markdown_content)
            for text, link_url in markdown_links:
                if link_url.startswith(('http://', 'https://')):
                    absolute_url = link_url
                else:
                    absolute_url = urljoin(url, link_url)

                if self._is_internal_link(absolute_url) and not self._is_widget_link(absolute_url):
                    links.append(absolute_url)

            # Find plain URLs in the text
            plain_urls = re.findall(r'https?://[^\s\)]+', markdown_content)
            for link_url in plain_urls:
                if self._is_internal_link(link_url) and not self._is_widget_link(link_url):
                    if link_url not in links:
                        links.append(link_url)

            return markdown_content, links

        except Exception as e:
            print(f"Error parsing {url}: {str(e)}")
            if isinstance(self.failure_tracker, ProcessingTracker):
                self.failure_tracker.update_status(
                    url,
                    ProcessingStage.CRAWLING,
                    StatusCode.FAILURE,
                    details=f"Failed to parse page: {str(e)}"
                )
            return None, []

    def _make_api_request(self, url):
        """Make a request to the Scrape Ninja API to fetch page content as markdown."""
        try:
            headers = {
                "Content-Type": "application/json",
                "x-apiroad-key": self.api_key
            }

            payload = {
                "url": url,
                "method": "GET",
                "retryNum": 3,
                "geo": "us",
                "extractorPreset": "markdown"
            }

            for retry_count in range(self.max_retries):
                try:
                    response = self.session.post(self.endpoint, headers=headers, json=payload)
                    response.raise_for_status()
                    response_data = response.json()

                    # Check if the response is successful
                    if response_data.get('success'):
                        # Access the markdown content from the extractor result
                        extractor = response_data.get('extractor', {})
                        result = extractor.get('result', {})
                        markdown_content = result.get('markdown', '')

                        if markdown_content and len(markdown_content) >= 50:
                            return markdown_content

                    # If we didn't get good content, try again
                    if retry_count < self.max_retries - 1:
                        time.sleep(self.retry_delay * (2 ** retry_count))
                        continue

                    raise Exception(f"Failed to get valid response from Scrape Ninja API after {self.max_retries} retries")

                except Exception as e:
                    if retry_count < self.max_retries - 1:
                        time.sleep(self.retry_delay * (2 ** retry_count))
                        continue
                    raise Exception(f"Scrape Ninja API request failed: {str(e)}")

            return None


    def process_with_gemini(self, output_dir=None, no_preprocess=False, location_info=None):
        """
        Process the crawled content with Gemini API for parsing.

        Args:
            output_dir: Directory to save parsed results
            no_preprocess: Whether to skip preprocessing
            location_info: Optional location information from CSV for the parser
        """
        print(f"Processing crawled content with Gemini API...")

        # Check if the output file exists
        if not os.path.exists(self.output_file):
            print(f"Error: Output file {self.output_file} not found.")
            return

        # First, apply preprocessing if not disabled and not already applied
        # This will create a preprocessed version of the content and update the output file
        if not no_preprocess and not self.no_preprocess and not self.preprocessing_applied:
            print("Applying preprocessing before sending to Gemini API...")
            preprocessed_file_path = self.process_with_preprocessing(output_dir=output_dir)
            if not preprocessed_file_path:
                print("Warning: Preprocessing failed, will use original content")
        elif self.preprocessing_applied:
            print("Preprocessing already applied, skipping redundant preprocessing")

        # Read the content (which may now be preprocessed)
        with open(self.output_file, 'r', encoding='utf-8') as f:
            content = f.read()

        try:
            # Get the API key
            api_key = os.environ.get('GEMINI_API_KEY', GEMINI_API_KEY)
            if not api_key:
                print("Error: Gemini API key is required for parsing.")
                print("Please set the GEMINI_API_KEY environment variable.")
                return

            # Ensure we have a valid output directory
            if not output_dir:
                output_dir = "parsed_results"
                print(f"No output directory specified, using default: {output_dir}")

            # Create main output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)
            print(f"Saving results to {output_dir}")

            # Create a subfolder for raw and intermediate files
            raw_dir = os.path.join(output_dir, "raw")
            os.makedirs(raw_dir, exist_ok=True)

            # Always use the original domain name (not redirected) for file naming consistency
            # This ensures all files related to the same website use the same base name
            original_domain = self.domain
            if self.redirected_domain:
                print(f"Using original domain {original_domain} for file naming (instead of redirected domain {self.redirected_domain})")
            base_filename = original_domain

            # JSON output file - in the main output directory
            json_output_file = os.path.join(output_dir, f"{base_filename}_{self.timestamp}.json")

            # Initialize the parser and parse the content
            from gemini_parser import GeminiParser
            from gemini_api import clean_json_data

            parser = GeminiParser(api_key=api_key)

            # Set location info if provided (from CSV)
            if location_info:
                parser.set_location_info(location_info)

            # Parse the content
            print("Sending content to Gemini API for parsing...")

            # Since we've already preprocessed the content above, we can skip preprocessing here
            parsed_data = parser.parse_content(content, save_preprocessed=False, skip_preprocessing=True, homepage_url=self.start_url)

            if not parsed_data:
                error_msg = "Gemini API returned empty or invalid data"
                print(f"Warning: {error_msg}")
                self.failure_tracker.add_parse_failure(self.start_url, error_msg)
                return

            # Check if this is partial data from a truncated response
            if parsed_data.get('partial_data'):
                print("Warning: Using partial data extracted from truncated Gemini API response")
                print("Some fields may be missing or incomplete")
                # Continue processing with the partial data we have

            # Clean the JSON data to remove special characters
            cleaned_data = clean_json_data(parsed_data)

            # Save the cleaned parsed data to a JSON file
            with open(json_output_file, 'w', encoding='utf-8') as f:
                json.dump(cleaned_data, f, indent=2, ensure_ascii=False)

            print(f"Parsed data saved to {json_output_file}")

            # Sort JSON files into Dog_cat folder if they only contain cats and/or dogs
            sort_json_files(output_dir=output_dir)

            return json_output_file

        except Exception as e:
            error_msg = f"Error processing content with Gemini API: {e}"
            print(error_msg)
            self.failure_tracker.add_parse_failure(self.start_url, error_msg)
            return None



if __name__ == "__main__":
    main()
