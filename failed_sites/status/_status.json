{"https://allcreaturespetvet.com/?utm_source=gmb&utm_medium=organic": {"url": "https://allcreaturespetvet.com/?utm_source=gmb&utm_medium=organic", "domain": "allcreaturespetvet.com", "first_seen": "2025-05-26T16:06:17.484393", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.536060", "details": "Crawling failed: Error processing https://allcreaturespetvet.com/?utm_source=gmb&utm_medium=organic: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.536064"}, "https://exoticpetswichita.com/": {"url": "https://exoticpetswichita.com/", "domain": "exoticpetswichita.com", "first_seen": "2025-05-26T16:06:17.485524", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.537397", "details": "Crawling failed: Error processing https://exoticpetswichita.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.537401"}, "http://exoticpetswichita.com/": {"url": "http://exoticpetswichita.com/", "domain": "exoticpetswichita.com", "first_seen": "2025-05-26T16:06:18.998389", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.538210", "details": "Crawling failed: Error processing http://exoticpetswichita.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.538212"}, "https://www.vetinwichita.com/": {"url": "https://www.vetinwichita.com/", "domain": "vetinwichita.com", "first_seen": "2025-05-26T16:06:19.000315", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.539144", "details": "Crawling failed: Error processing https://www.vetinwichita.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.539147"}, "https://cimarronanimalhospital.com/": {"url": "https://cimarronanimalhospital.com/", "domain": "cimarronanimalhospital.com", "first_seen": "2025-05-26T16:06:20.131046", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.543413", "details": "Crawling failed: Error processing https://cimarronanimalhospital.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.543417"}, "http://bogue-animal-hospital.book-myvet.com/": {"url": "http://bogue-animal-hospital.book-myvet.com/", "domain": "bogue-animal-hospital.book-myvet.com", "first_seen": "2025-05-26T16:06:21.235375", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.540720", "details": "Crawling failed: Error processing http://bogue-animal-hospital.book-myvet.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.540722"}, "https://bogue-animal-hospital.book-myvet.com/": {"url": "https://bogue-animal-hospital.book-myvet.com/", "domain": "bogue-animal-hospital.book-myvet.com", "first_seen": "2025-05-26T16:06:23.344734", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:07:57.128393", "details": "Crawling failed: HTTP error: 402 Client Error: Payment Required for url: https://r.jina.ai/"}}, "overall_status": "failure", "last_updated": "2025-05-26T16:07:57.128413"}, "https://guptonspetcare.com/": {"url": "https://guptonspetcare.com/", "domain": "guptonspetcare.com", "first_seen": "2025-05-26T16:06:23.346778", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.541460", "details": "Crawling failed: Error processing https://guptonspetcare.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.541462"}, "https://www.vetwichita.com/?y_source=1_MTUyMjkzNTAtNzE1LWxvY2F0aW9uLndlYnNpdGU%3D": {"url": "https://www.vetwichita.com/?y_source=1_MTUyMjkzNTAtNzE1LWxvY2F0aW9uLndlYnNpdGU%3D", "domain": "vetwichita.com", "first_seen": "2025-05-26T16:06:24.692069", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.542161", "details": "Crawling failed: Error processing https://www.vetwichita.com/?y_source=1_MTUyMjkzNTAtNzE1LWxvY2F0aW9uLndlYnNpdGU%3D: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.542163"}, "https://northrockvet.com/?utm_source=gmb&utm_medium=organic": {"url": "https://northrockvet.com/?utm_source=gmb&utm_medium=organic", "domain": "northrockvet.com", "first_seen": "2025-05-26T16:06:25.797419", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.545481", "details": "Crawling failed: Error processing https://northrockvet.com/?utm_source=gmb&utm_medium=organic: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.545484"}, "http://www.ihacvet.com/": {"url": "http://www.ihacvet.com/", "domain": "ihacvet.com", "first_seen": "2025-05-26T16:06:25.804207", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.546356", "details": "Crawling failed: Error processing http://www.ihacvet.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.546359"}, "https://www.ihacvet.com/": {"url": "https://www.ihacvet.com/", "domain": "ihacvet.com", "first_seen": "2025-05-26T16:06:27.708409", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:08:01.469492", "details": "Crawling failed: HTTP error: 402 Client Error: Payment Required for url: https://r.jina.ai/"}}, "overall_status": "failure", "last_updated": "2025-05-26T16:08:01.469507"}, "http://allcreaturespetvet.com/": {"url": "http://allcreaturespetvet.com/", "domain": "allcreaturespetvet.com", "first_seen": "2025-05-26T16:06:27.718254", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.547207", "details": "Crawling failed: Error processing http://allcreaturespetvet.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.547209"}, "http://westwichitapetclinic.net/": {"url": "http://westwichitapetclinic.net/", "domain": "westwichitapetclinic.net", "first_seen": "2025-05-26T16:06:27.720328", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.548260", "details": "Crawling failed: Error processing http://westwichitapetclinic.net/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.548262"}, "https://westwichitapetclinic.net/": {"url": "https://westwichitapetclinic.net/", "domain": "westwichitapetclinic.net", "first_seen": "2025-05-26T16:06:29.697701", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:08:03.405630", "details": "Crawling failed: HTTP error: 402 Client Error: Payment Required for url: https://r.jina.ai/"}}, "overall_status": "failure", "last_updated": "2025-05-26T16:08:03.405647"}, "https://animalhealthcenterwichita.com/": {"url": "https://animalhealthcenterwichita.com/", "domain": "animalhealthcenterwichita.com", "first_seen": "2025-05-26T16:06:29.707086", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.549471", "details": "Crawling failed: Error processing https://animalhealthcenterwichita.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.549473"}, "https://www.facebook.com/wichitareptarium/": {"url": "https://www.facebook.com/wichitareptarium/", "domain": "facebook.com", "first_seen": "2025-05-26T16:06:30.815347", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.551175", "details": "Crawling failed: Error processing https://www.facebook.com/wichitareptarium/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.551178"}, "http://www.skaervet.com/": {"url": "http://www.skaervet.com/", "domain": "skaervet.com", "first_seen": "2025-05-26T16:06:31.940840", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.552074", "details": "Crawling failed: Error processing http://www.skaervet.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.552076"}, "https://www.skaervet.com/": {"url": "https://www.skaervet.com/", "domain": "skaervet.com", "first_seen": "2025-05-26T16:06:34.404707", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:08:07.432585", "details": "Crawling failed: HTTP error: 402 Client Error: Payment Required for url: https://r.jina.ai/"}}, "overall_status": "failure", "last_updated": "2025-05-26T16:08:07.432604"}, "http://www.hodesvhc.com/": {"url": "http://www.hodesvhc.com/", "domain": "hodesvhc.com", "first_seen": "2025-05-26T16:06:34.415740", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.553071", "details": "Crawling failed: Error processing http://www.hodesvhc.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.553074"}, "https://www.hodesvhc.com/": {"url": "https://www.hodesvhc.com/", "domain": "hodesvhc.com", "first_seen": "2025-05-26T16:06:36.195234", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:08:09.214570", "details": "Crawling failed: HTTP error: 402 Client Error: Payment Required for url: https://r.jina.ai/"}}, "overall_status": "failure", "last_updated": "2025-05-26T16:08:09.214590"}, "https://www.prairieridgeah.com/?utm_source=gmb&utm_medium=organic&y_source=1_MjQwMjM3MzEtNzE1LWxvY2F0aW9uLndlYnNpdGU%3D": {"url": "https://www.prairieridgeah.com/?utm_source=gmb&utm_medium=organic&y_source=1_MjQwMjM3MzEtNzE1LWxvY2F0aW9uLndlYnNpdGU%3D", "domain": "prairieridgeah.com", "first_seen": "2025-05-26T16:06:36.201704", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.553855", "details": "Crawling failed: Error processing https://www.prairieridgeah.com/?utm_source=gmb&utm_medium=organic&y_source=1_MjQwMjM3MzEtNzE1LWxvY2F0aW9uLndlYnNpdGU%3D: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.553857"}, "http://ctahvets.com/": {"url": "http://ctahvets.com/", "domain": "ctahvets.com", "first_seen": "2025-05-26T16:06:36.210286", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.555010", "details": "Crawling failed: Error processing http://ctahvets.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.555014"}, "https://ctahvets.com/": {"url": "https://ctahvets.com/", "domain": "ctahvets.com", "first_seen": "2025-05-26T16:06:37.972524", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:08:11.375866", "details": "Crawling failed: HTTP error: 402 Client Error: Payment Required for url: https://r.jina.ai/"}}, "overall_status": "failure", "last_updated": "2025-05-26T16:08:11.375883"}, "http://sistersveterinaryclinic.com/": {"url": "http://sistersveterinaryclinic.com/", "domain": "sistersveterinaryclinic.com", "first_seen": "2025-05-26T16:06:37.984944", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.556338", "details": "Crawling failed: Error processing http://sistersveterinaryclinic.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.556340"}, "https://sistersveterinaryclinic.com/": {"url": "https://sistersveterinaryclinic.com/", "domain": "sistersveterinaryclinic.com", "first_seen": "2025-05-26T16:06:39.670905", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:08:13.185985", "details": "Crawling failed: HTTP error: 402 Client Error: Payment Required for url: https://r.jina.ai/"}}, "overall_status": "failure", "last_updated": "2025-05-26T16:08:13.186003"}, "http://solomonvet.com/": {"url": "http://solomonvet.com/", "domain": "solomonvet.com", "first_seen": "2025-05-26T16:06:39.682951", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.557426", "details": "Crawling failed: Error processing http://solomonvet.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.557428"}, "https://solomonvet.com/": {"url": "https://solomonvet.com/", "domain": "solomonvet.com", "first_seen": "2025-05-26T16:06:41.664594", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:08:14.953946", "details": "Crawling failed: HTTP error: 402 Client Error: Payment Required for url: https://r.jina.ai/"}}, "overall_status": "failure", "last_updated": "2025-05-26T16:08:14.953968"}, "https://auburnhillsvet.com/": {"url": "https://auburnhillsvet.com/", "domain": "auburnhillsvet.com", "first_seen": "2025-05-26T16:06:41.677858", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.558527", "details": "Crawling failed: Error processing https://auburnhillsvet.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.558529"}, "http://pawshvet.com/": {"url": "http://pawshvet.com/", "domain": "pawshvet.com", "first_seen": "2025-05-26T16:06:42.788844", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.559729", "details": "Crawling failed: Error processing http://pawshvet.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.559733"}, "https://pawshvet.com/": {"url": "https://pawshvet.com/", "domain": "pawshvet.com", "first_seen": "2025-05-26T16:06:44.356250", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:08:17.708678", "details": "Crawling failed: HTTP error: 402 Client Error: Payment Required for url: https://r.jina.ai/"}}, "overall_status": "failure", "last_updated": "2025-05-26T16:08:17.708693"}, "https://southsidevc.com/": {"url": "https://southsidevc.com/", "domain": "southsidevc.com", "first_seen": "2025-05-26T16:06:44.365402", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.561699", "details": "Crawling failed: Error processing https://southsidevc.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.561702"}, "http://www.pets-doctor.com/": {"url": "http://www.pets-doctor.com/", "domain": "pets-doctor.com", "first_seen": "2025-05-26T16:06:45.483839", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.563056", "details": "Crawling failed: Error processing http://www.pets-doctor.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.563058"}, "https://www.pets-doctor.com/": {"url": "https://www.pets-doctor.com/", "domain": "pets-doctor.com", "first_seen": "2025-05-26T16:06:47.187424", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:08:20.646389", "details": "Crawling failed: HTTP error: 402 Client Error: Payment Required for url: https://r.jina.ai/"}}, "overall_status": "failure", "last_updated": "2025-05-26T16:08:20.646412"}}