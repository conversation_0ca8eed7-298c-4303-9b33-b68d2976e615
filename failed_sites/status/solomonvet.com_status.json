{"http://solomonvet.com/": {"url": "http://solomonvet.com/", "domain": "solomonvet.com", "first_seen": "2025-05-26T16:06:39.682951", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.557426", "details": "Crawling failed: Error processing http://solomonvet.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.557428"}, "https://solomonvet.com/": {"url": "https://solomonvet.com/", "domain": "solomonvet.com", "first_seen": "2025-05-26T16:06:41.664594", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:08:14.953946", "details": "Crawling failed: HTTP error: 402 Client Error: Payment Required for url: https://r.jina.ai/"}}, "overall_status": "failure", "last_updated": "2025-05-26T16:08:14.953968"}}