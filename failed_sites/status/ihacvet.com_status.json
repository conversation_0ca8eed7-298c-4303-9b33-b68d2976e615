{"http://www.ihacvet.com/": {"url": "http://www.ihacvet.com/", "domain": "ihacvet.com", "first_seen": "2025-05-26T16:06:25.804207", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.546356", "details": "Crawling failed: Error processing http://www.ihacvet.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.546359"}, "https://www.ihacvet.com/": {"url": "https://www.ihacvet.com/", "domain": "ihacvet.com", "first_seen": "2025-05-26T16:06:27.708409", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:08:01.469492", "details": "Crawling failed: HTTP error: 402 Client Error: Payment Required for url: https://r.jina.ai/"}}, "overall_status": "failure", "last_updated": "2025-05-26T16:08:01.469507"}}