{"http://pawshvet.com/": {"url": "http://pawshvet.com/", "domain": "pawshvet.com", "first_seen": "2025-05-26T16:06:42.788844", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:18:26.559729", "details": "Crawling failed: Error processing http://pawshvet.com/: Scrape Ninja API key not found. Set SCRAPENINJA_API_KEY environment variable."}}, "overall_status": "failure", "last_updated": "2025-05-26T16:18:26.559733"}, "https://pawshvet.com/": {"url": "https://pawshvet.com/", "domain": "pawshvet.com", "first_seen": "2025-05-26T16:06:44.356250", "stages": {"crawling": {"status": "failure", "timestamp": "2025-05-26T16:08:17.708678", "details": "Crawling failed: HTTP error: 402 Client Error: Payment Required for url: https://r.jina.ai/"}}, "overall_status": "failure", "last_updated": "2025-05-26T16:08:17.708693"}}