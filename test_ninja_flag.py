#!/usr/bin/env python3
"""
Test script to verify the --ninja flag implementation.
This script tests the basic functionality without making actual API calls.
"""

import os
import sys
import argparse
from unittest.mock import patch, MagicMock

# Add the current directory to the path so we can import parser
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_argument_parsing():
    """Test that the --ninja argument is properly parsed."""
    print("Testing argument parsing...")
    
    # Import the main function from parser
    from parser import main
    
    # Test that --ninja flag is recognized
    test_args = ['test_url.com', '--ninja']
    
    with patch('sys.argv', ['parser.py'] + test_args):
        with patch('parser.ScrapeNinjaCrawler') as mock_crawler:
            with patch('os.environ.get') as mock_env:
                # Mock the API key
                mock_env.return_value = 'test-api-key'
                
                # Mock the crawler instance
                mock_instance = MagicMock()
                mock_crawler.return_value = mock_instance
                
                try:
                    main()
                    print("✓ --ninja flag is properly recognized")
                    print("✓ ScrapeNinjaCrawler is instantiated when --ninja flag is used")
                    return True
                except SystemExit:
                    # This is expected when parser.error() is called
                    pass
                except Exception as e:
                    print(f"✗ Error during argument parsing: {e}")
                    return False

def test_incompatible_flags():
    """Test that incompatible flags are properly detected."""
    print("\nTesting incompatible flag detection...")
    
    from parser import main
    
    # Test --ninja with --scrapeless
    test_args = ['test_url.com', '--ninja', '--scrapeless']
    
    with patch('sys.argv', ['parser.py'] + test_args):
        try:
            main()
            print("✗ Should have detected incompatible flags")
            return False
        except SystemExit:
            print("✓ Incompatible flags (--ninja + --scrapeless) properly detected")
            
    # Test --ninja with --jina-direct
    test_args = ['test_url.com', '--ninja', '--jina-direct']
    
    with patch('sys.argv', ['parser.py'] + test_args):
        try:
            main()
            print("✗ Should have detected incompatible flags")
            return False
        except SystemExit:
            print("✓ Incompatible flags (--ninja + --jina-direct) properly detected")
            return True

def test_scrape_ninja_crawler_import():
    """Test that ScrapeNinjaCrawler can be imported and instantiated."""
    print("\nTesting ScrapeNinjaCrawler import and instantiation...")
    
    try:
        from parser import ScrapeNinjaCrawler
        print("✓ ScrapeNinjaCrawler successfully imported")
        
        # Mock the environment variable
        with patch.dict(os.environ, {'SCRAPENINJA_API_KEY': 'test-api-key'}):
            # Mock the session and other dependencies
            with patch('parser.requests.Session'):
                crawler = ScrapeNinjaCrawler(
                    start_url='https://example.com',
                    max_pages=1,
                    delay=0
                )
                print("✓ ScrapeNinjaCrawler successfully instantiated")
                print(f"✓ API endpoint: {crawler.endpoint}")
                print(f"✓ Max retries: {crawler.max_retries}")
                return True
                
    except Exception as e:
        print(f"✗ Error importing or instantiating ScrapeNinjaCrawler: {e}")
        return False

def test_api_key_validation():
    """Test that API key validation works properly."""
    print("\nTesting API key validation...")
    
    try:
        from parser import ScrapeNinjaCrawler
        
        # Test without API key
        with patch.dict(os.environ, {}, clear=True):
            try:
                crawler = ScrapeNinjaCrawler(start_url='https://example.com')
                print("✗ Should have raised ValueError for missing API key")
                return False
            except ValueError as e:
                if "SCRAPENINJA_API_KEY" in str(e):
                    print("✓ Properly validates missing API key")
                else:
                    print(f"✗ Wrong error message: {e}")
                    return False
        
        # Test with API key
        with patch.dict(os.environ, {'SCRAPENINJA_API_KEY': 'test-key'}):
            with patch('parser.requests.Session'):
                crawler = ScrapeNinjaCrawler(start_url='https://example.com')
                print("✓ Accepts valid API key")
                return True
                
    except Exception as e:
        print(f"✗ Unexpected error during API key validation: {e}")
        return False

def test_help_text():
    """Test that help text includes --ninja flag."""
    print("\nTesting help text...")
    
    from parser import main
    
    with patch('sys.argv', ['parser.py', '--help']):
        try:
            main()
        except SystemExit:
            # Help text should have been printed
            pass
    
    # We can't easily capture the help output, but we can check if the argument is defined
    import parser
    import argparse
    
    # Create a parser like the one in main()
    test_parser = argparse.ArgumentParser()
    test_parser.add_argument('--ninja', '-n', action='store_true', 
                           help='Use Scrape Ninja API to render JavaScript and fetch content with markdown extraction')
    
    # Test parsing
    args = test_parser.parse_args(['--ninja'])
    if args.ninja:
        print("✓ --ninja flag properly defined in argument parser")
        return True
    else:
        print("✗ --ninja flag not working in argument parser")
        return False

def main():
    """Run all tests."""
    print("=== Testing --ninja Flag Implementation ===\n")
    
    tests = [
        test_argument_parsing,
        test_incompatible_flags,
        test_scrape_ninja_crawler_import,
        test_api_key_validation,
        test_help_text
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    print(f"Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All tests passed! The --ninja flag implementation looks good.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
